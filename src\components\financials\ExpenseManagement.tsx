import React, { useState } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Badge } from '@/components/ui/badge';
import {
  Edit,
  Trash2,
  Plus,
  Search,
  Filter,
  DollarSign,
  Calendar,
  Receipt,
  Building,
  User,
  PieChart,
  AlertCircle,
  Database
} from 'lucide-react';
import { useToast } from '@/components/ui/use-toast';
import { FinancialService } from '@/lib/financials';
import { CompanyExpense, CompanyExpenseCategory, CompanyExpenseService } from '@/lib/companyExpenseServiceNew';

interface ExpenseManagementProps {
  expenses: CompanyExpense[];
  expenseCategories: CompanyExpenseCategory[];
  onCreateExpense: (expense: Partial<CompanyExpense>) => Promise<void>;
  onUpdateExpense: (id: string, updates: Partial<CompanyExpense>) => Promise<void>;
  onDeleteExpense: (id: string) => Promise<void>;
}

const ExpenseManagement: React.FC<ExpenseManagementProps> = ({
  expenses,
  expenseCategories,
  onCreateExpense,
  onUpdateExpense,
  onDeleteExpense
}) => {
  const { toast } = useToast();
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false);
  const [isCreateDialogOpen, setIsCreateDialogOpen] = useState(false);
  const [isCategoryDialogOpen, setIsCategoryDialogOpen] = useState(false);
  const [selectedExpense, setSelectedExpense] = useState<CompanyExpense | null>(null);
  const [editingCategory, setEditingCategory] = useState<CompanyExpenseCategory | null>(null);
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState<string>('all');
  const [categoryFilter, setCategoryFilter] = useState<string>('all');
  const [loading, setLoading] = useState(false);
  const [syncingPayroll, setSyncingPayroll] = useState(false);

  const [formData, setFormData] = useState({
    category_id: '',
    description: '',
    amount: '',
    expense_date: new Date().toISOString().split('T')[0],
    payment_method: '',
    vendor_name: '',
    status: 'pending' as const
  });

  const [categoryFormData, setCategoryFormData] = useState({
    name: '',
    description: '',
    color: '#3B82F6',
    icon: '📦',
    is_project_related: false
  });

  const paymentMethods = [
    'Cash',
    'Bank Transfer',
    'Credit Card',
    'Debit Card',
    'Check',
    'Online Payment'
  ];

  const statusOptions = [
    { value: 'pending', label: 'Pending', variant: 'secondary' as const },
    { value: 'paid', label: 'Paid', variant: 'default' as const },
    { value: 'cancelled', label: 'Cancelled', variant: 'destructive' as const }
  ];

  // Filter expenses based on search and filters
  const filteredExpenses = expenses.filter(expense => {
    const matchesSearch = expense.description.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         expense.vendor_name?.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         expense.category?.name?.toLowerCase().includes(searchTerm.toLowerCase());
    
    const matchesStatus = statusFilter === 'all' || expense.status === statusFilter;
    const matchesCategory = categoryFilter === 'all' || expense.category_id === categoryFilter;
    
    return matchesSearch && matchesStatus && matchesCategory;
  });

  const handleEdit = (expense: CompanyExpense) => {
    setSelectedExpense(expense);
    setFormData({
      category_id: expense.category_id || '',
      description: expense.description,
      amount: expense.amount.toString(),
      expense_date: expense.expense_date,
      payment_method: expense.payment_method || '',
      vendor_name: expense.vendor_name || '',
      status: expense.status
    });
    setIsEditDialogOpen(true);
  };

  const handleCreate = () => {
    setSelectedExpense(null);
    setFormData({
      category_id: '',
      description: '',
      amount: '',
      expense_date: new Date().toISOString().split('T')[0],
      payment_method: '',
      vendor_name: '',
      status: 'pending'
    });
    setIsCreateDialogOpen(true);
  };

  const handleSubmit = async () => {
    if (!formData.description || !formData.amount || !formData.category_id) {
      toast({
        title: "Validation Error",
        description: "Please fill in all required fields",
        variant: "destructive",
      });
      return;
    }

    try {
      setLoading(true);

      const expenseData = {
        category_id: formData.category_id,
        description: formData.description,
        amount: parseFloat(formData.amount),
        expense_date: formData.expense_date,
        payment_method: formData.payment_method,
        vendor_name: formData.vendor_name,
        status: formData.status
      };

      if (selectedExpense) {
        await onUpdateExpense(selectedExpense.id, expenseData);
        toast({
          title: "Success",
          description: "Expense updated successfully",
        });
        setIsEditDialogOpen(false);
      } else {
        await onCreateExpense(expenseData);
        toast({
          title: "Success",
          description: "Expense created successfully",
        });
        setIsCreateDialogOpen(false);
      }
    } catch (error) {
      toast({
        title: "Error",
        description: selectedExpense ? "Failed to update expense" : "Failed to create expense",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  const handleDelete = async (expenseId: string) => {
    if (!confirm('Are you sure you want to delete this expense?')) {
      return;
    }

    try {
      await onDeleteExpense(expenseId);
      toast({
        title: "Success",
        description: "Expense deleted successfully",
      });
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to delete expense",
        variant: "destructive",
      });
    }
  };

  const handleSyncPayroll = async () => {
    try {
      setSyncingPayroll(true);

      const result = await FinancialService.syncAllPayrollExpenses();

      if (result.processed > 0) {
        toast({
          title: "Payroll Sync Complete",
          description: `Successfully synced ${result.processed} payroll periods to expenses. ${result.errors > 0 ? `${result.errors} errors occurred.` : ''}`,
        });

        // Refresh the expense list
        window.location.reload();
      } else {
        toast({
          title: "No New Payroll Data",
          description: "All payroll periods are already synced to expenses.",
        });
      }
    } catch (error) {
      toast({
        title: "Sync Error",
        description: "Failed to sync payroll expenses. Please try again.",
        variant: "destructive",
      });
    } finally {
      setSyncingPayroll(false);
    }
  };

  const getTotalAmount = () => {
    return filteredExpenses.reduce((sum, expense) => sum + expense.amount, 0);
  };

  const getPaidAmount = () => {
    return filteredExpenses
      .filter(expense => expense.status === 'paid')
      .reduce((sum, expense) => sum + expense.amount, 0);
  };

  const getPendingAmount = () => {
    return filteredExpenses
      .filter(expense => expense.status === 'pending')
      .reduce((sum, expense) => sum + expense.amount, 0);
  };

  // Category management functions
  const handleCategorySubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    try {
      if (editingCategory) {
        await CompanyExpenseService.updateCompanyExpenseCategory(editingCategory.id, categoryFormData);
        toast({
          title: "Success",
          description: "Category updated successfully"
        });
      } else {
        await CompanyExpenseService.createCompanyExpenseCategory(categoryFormData);
        toast({
          title: "Success",
          description: "Category created successfully"
        });
      }

      resetCategoryForm();
      setIsCategoryDialogOpen(false);
      window.location.reload();
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to save category",
        variant: "destructive"
      });
    }
  };

  const resetCategoryForm = () => {
    setCategoryFormData({
      name: '',
      description: '',
      color: '#3B82F6',
      icon: '📦',
      is_project_related: false
    });
    setEditingCategory(null);
  };

  return (
    <div className="space-y-6">
      {/* Database Setup Warning */}
      {expenseCategories.length === 0 && (
        <Card className="border-yellow-200 bg-yellow-50">
          <CardContent className="p-6">
            <div className="flex items-start space-x-3">
              <AlertCircle className="w-6 h-6 text-yellow-600 mt-0.5" />
              <div className="flex-1">
                <h4 className="font-medium text-yellow-900 mb-2">Database Setup Required</h4>
                <p className="text-sm text-yellow-800 mb-3">
                  The company expense categories table is missing. You need to set up the database before you can manage company expenses.
                </p>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => window.open('/settings', '_blank')}
                  className="bg-white hover:bg-yellow-50"
                >
                  <Database className="w-4 h-4 mr-2" />
                  Go to Database Setup
                </Button>
              </div>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Summary Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center">
              <Receipt className="h-8 w-8 text-blue-600" />
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">Total Expenses</p>
                <p className="text-2xl font-bold">${getTotalAmount().toLocaleString()}</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center">
              <DollarSign className="h-8 w-8 text-green-600" />
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">Paid</p>
                <p className="text-2xl font-bold text-green-600">${getPaidAmount().toLocaleString()}</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center">
              <Calendar className="h-8 w-8 text-orange-600" />
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">Pending</p>
                <p className="text-2xl font-bold text-orange-600">${getPendingAmount().toLocaleString()}</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center">
              <Building className="h-8 w-8 text-purple-600" />
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">Total Count</p>
                <p className="text-2xl font-bold">{filteredExpenses.length}</p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Controls */}
      <Card>
        <CardHeader>
          <div className="flex justify-between items-center">
            <CardTitle>Expense Management</CardTitle>
            <div className="flex space-x-2">
              <Button
                variant="outline"
                onClick={() => {
                  resetCategoryForm();
                  setIsCategoryDialogOpen(true);
                }}
              >
                <PieChart className="w-4 h-4 mr-2" />
                Manage Categories
              </Button>
              <Button
                variant="outline"
                onClick={handleSyncPayroll}
                disabled={syncingPayroll}
              >
                <User className="w-4 h-4 mr-2" />
                {syncingPayroll ? 'Syncing...' : 'Sync Payroll'}
              </Button>
              <Button onClick={handleCreate}>
                <Plus className="w-4 h-4 mr-2" />
                Add Expense
              </Button>
            </div>
          </div>
        </CardHeader>
        <CardContent>
          <div className="flex flex-col md:flex-row gap-4 mb-6">
            <div className="flex-1">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                <Input
                  placeholder="Search expenses..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10"
                />
              </div>
            </div>
            
            <Select value={statusFilter} onValueChange={setStatusFilter}>
              <SelectTrigger className="w-40">
                <SelectValue placeholder="Filter by status" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Status</SelectItem>
                <SelectItem value="pending">Pending</SelectItem>
                <SelectItem value="paid">Paid</SelectItem>
                <SelectItem value="cancelled">Cancelled</SelectItem>
              </SelectContent>
            </Select>

            <Select value={categoryFilter} onValueChange={setCategoryFilter}>
              <SelectTrigger className="w-48">
                <SelectValue placeholder="Filter by category" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Categories</SelectItem>
                {expenseCategories.map((category) => (
                  <SelectItem key={category.id} value={category.id}>
                    {category.name}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          {/* Expenses Table */}
          <div className="border rounded-lg">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Description</TableHead>
                  <TableHead>Category</TableHead>
                  <TableHead>Vendor</TableHead>
                  <TableHead>Amount</TableHead>
                  <TableHead>Date</TableHead>
                  <TableHead>Payment Method</TableHead>
                  <TableHead>Status</TableHead>
                  <TableHead className="text-right">Actions</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {filteredExpenses.map((expense) => (
                  <TableRow key={expense.id}>
                    <TableCell className="font-medium">{expense.description}</TableCell>
                    <TableCell>{expense.category?.name || 'Uncategorized'}</TableCell>
                    <TableCell>{expense.vendor_name || '-'}</TableCell>
                    <TableCell className="font-bold">${expense.amount.toLocaleString()}</TableCell>
                    <TableCell>{new Date(expense.expense_date).toLocaleDateString()}</TableCell>
                    <TableCell>{expense.payment_method || '-'}</TableCell>
                    <TableCell>
                      <Badge variant={
                        expense.status === 'paid' ? 'default' :
                        expense.status === 'pending' ? 'secondary' : 'destructive'
                      }>
                        {expense.status}
                      </Badge>
                    </TableCell>
                    <TableCell className="text-right">
                      <div className="flex justify-end space-x-2">
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => handleEdit(expense)}
                        >
                          <Edit className="w-4 h-4" />
                        </Button>
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => handleDelete(expense.id)}
                          className="text-red-600 hover:text-red-700"
                        >
                          <Trash2 className="w-4 h-4" />
                        </Button>
                      </div>
                    </TableCell>
                  </TableRow>
                ))}
                {filteredExpenses.length === 0 && (
                  <TableRow>
                    <TableCell colSpan={8} className="text-center py-8">
                      <Receipt className="w-12 h-12 text-gray-400 mx-auto mb-4" />
                      <p className="text-gray-500">No expenses found</p>
                    </TableCell>
                  </TableRow>
                )}
              </TableBody>
            </Table>
          </div>
        </CardContent>
      </Card>

      {/* Create/Edit Dialog */}
      <Dialog open={isCreateDialogOpen || isEditDialogOpen} onOpenChange={(open) => {
        if (!open) {
          setIsCreateDialogOpen(false);
          setIsEditDialogOpen(false);
          setSelectedExpense(null);
        }
      }}>
        <DialogContent className="sm:max-w-[600px]">
          <DialogHeader>
            <DialogTitle>
              {selectedExpense ? 'Edit Expense' : 'Create New Expense'}
            </DialogTitle>
            <DialogDescription>
              {selectedExpense ? 'Update the expense details below.' : 'Fill in the details to create a new expense.'}
            </DialogDescription>
          </DialogHeader>

          <div className="grid gap-4 py-4">
            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="category">Category *</Label>
                <Select value={formData.category_id} onValueChange={(value) => setFormData(prev => ({ ...prev, category_id: value }))}>
                  <SelectTrigger>
                    <SelectValue placeholder="Select category" />
                  </SelectTrigger>
                  <SelectContent>
                    {expenseCategories.map((category) => (
                      <SelectItem key={category.id} value={category.id}>
                        {category.name}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              <div className="space-y-2">
                <Label htmlFor="amount">Amount *</Label>
                <Input
                  id="amount"
                  type="number"
                  step="0.01"
                  placeholder="0.00"
                  value={formData.amount}
                  onChange={(e) => setFormData(prev => ({ ...prev, amount: e.target.value }))}
                />
              </div>
            </div>

            <div className="space-y-2">
              <Label htmlFor="description">Description *</Label>
              <Input
                id="description"
                placeholder="Enter expense description"
                value={formData.description}
                onChange={(e) => setFormData(prev => ({ ...prev, description: e.target.value }))}
              />
            </div>

            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="vendor">Vendor/Supplier</Label>
                <Input
                  id="vendor"
                  placeholder="Enter vendor name"
                  value={formData.vendor_name}
                  onChange={(e) => setFormData(prev => ({ ...prev, vendor_name: e.target.value }))}
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="expense_date">Expense Date</Label>
                <Input
                  id="expense_date"
                  type="date"
                  value={formData.expense_date}
                  onChange={(e) => setFormData(prev => ({ ...prev, expense_date: e.target.value }))}
                />
              </div>
            </div>

            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="payment_method">Payment Method</Label>
                <Select value={formData.payment_method} onValueChange={(value) => setFormData(prev => ({ ...prev, payment_method: value }))}>
                  <SelectTrigger>
                    <SelectValue placeholder="Select payment method" />
                  </SelectTrigger>
                  <SelectContent>
                    {paymentMethods.map((method) => (
                      <SelectItem key={method} value={method}>
                        {method}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              <div className="space-y-2">
                <Label htmlFor="status">Status</Label>
                <Select value={formData.status} onValueChange={(value: any) => setFormData(prev => ({ ...prev, status: value }))}>
                  <SelectTrigger>
                    <SelectValue placeholder="Select status" />
                  </SelectTrigger>
                  <SelectContent>
                    {statusOptions.map((option) => (
                      <SelectItem key={option.value} value={option.value}>
                        {option.label}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
            </div>
          </div>

          <DialogFooter>
            <Button
              variant="outline"
              onClick={() => {
                setIsCreateDialogOpen(false);
                setIsEditDialogOpen(false);
                setSelectedExpense(null);
              }}
            >
              Cancel
            </Button>
            <Button onClick={handleSubmit} disabled={loading}>
              {loading ? 'Saving...' : (selectedExpense ? 'Update Expense' : 'Create Expense')}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Category Management Dialog */}
      <Dialog open={isCategoryDialogOpen} onOpenChange={setIsCategoryDialogOpen}>
        <DialogContent className="max-w-md">
          <DialogHeader>
            <DialogTitle>
              {editingCategory ? 'Edit Category' : 'Create New Category'}
            </DialogTitle>
          </DialogHeader>
          <form onSubmit={handleCategorySubmit} className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="category-name">Category Name *</Label>
              <Input
                id="category-name"
                value={categoryFormData.name}
                onChange={(e) => setCategoryFormData({...categoryFormData, name: e.target.value})}
                placeholder="e.g., Office Supplies"
                required
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="category-description">Description</Label>
              <Input
                id="category-description"
                value={categoryFormData.description}
                onChange={(e) => setCategoryFormData({...categoryFormData, description: e.target.value})}
                placeholder="Brief description of this category"
              />
            </div>

            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="category-color">Color</Label>
                <Input
                  id="category-color"
                  type="color"
                  value={categoryFormData.color}
                  onChange={(e) => setCategoryFormData({...categoryFormData, color: e.target.value})}
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="category-icon">Icon</Label>
                <Select value={categoryFormData.icon} onValueChange={(value) => setCategoryFormData({...categoryFormData, icon: value})}>
                  <SelectTrigger>
                    <SelectValue placeholder="Select icon" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="📎">📎 Office Supplies</SelectItem>
                    <SelectItem value="🔧">🔧 Equipment</SelectItem>
                    <SelectItem value="🧱">🧱 Materials</SelectItem>
                    <SelectItem value="💡">💡 Utilities</SelectItem>
                    <SelectItem value="🚗">🚗 Transportation</SelectItem>
                    <SelectItem value="💼">💼 Professional Services</SelectItem>
                    <SelectItem value="📢">📢 Marketing</SelectItem>
                    <SelectItem value="🛡️">🛡️ Insurance</SelectItem>
                    <SelectItem value="🔨">🔨 Maintenance</SelectItem>
                    <SelectItem value="📦">📦 Other</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>

            <div className="flex items-center space-x-2">
              <input
                type="checkbox"
                id="is-project-related"
                checked={categoryFormData.is_project_related}
                onChange={(e) => setCategoryFormData({...categoryFormData, is_project_related: e.target.checked})}
                className="rounded"
              />
              <Label htmlFor="is-project-related">Project-related expense</Label>
            </div>

            <div className="flex justify-end space-x-2">
              <Button type="button" variant="outline" onClick={() => setIsCategoryDialogOpen(false)}>
                Cancel
              </Button>
              <Button type="submit">
                {editingCategory ? 'Update Category' : 'Create Category'}
              </Button>
            </div>
          </form>
        </DialogContent>
      </Dialog>
    </div>
  );
};

export default ExpenseManagement;
