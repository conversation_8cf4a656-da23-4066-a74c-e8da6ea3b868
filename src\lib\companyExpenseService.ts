import { supabase } from './supabase'

export interface CompanyExpenseCategory {
  id: string
  name: string
  description?: string
  color: string
  icon: string
  is_project_related: boolean
  is_active: boolean
  created_at: string
  updated_at: string
}

export interface CompanyExpense {
  id: string
  category_id: string
  description: string
  amount: number
  expense_date: string
  payment_method: string
  vendor_name?: string
  status: 'pending' | 'approved' | 'paid' | 'rejected'
  is_recurring: boolean
  recurring_frequency?: string
  project_id?: string
  receipt_url?: string
  metadata?: any
  created_at: string
  updated_at: string
  category?: CompanyExpenseCategory
}

export interface ExpenseStats {
  total_expenses: number
  monthly_expenses: number
  pending_expenses: number
  approved_expenses: number
  categories_count: number
  average_expense: number
}

export class CompanyExpenseService {
  // Company Expense Categories
  static async getCompanyExpenseCategories(): Promise<CompanyExpenseCategory[]> {
    try {
      const { data, error } = await supabase
        .from('company_expense_categories')
        .select('*')
        .eq('is_active', true)
        .order('name')

      if (error) {
        console.error('Error fetching company expense categories:', error)
        // If table doesn't exist, return default categories
        if (error.code === '42P01') {
          console.log('Company expense categories table not found, using defaults...')
          return this.getDefaultCategories()
        }
        throw error
      }
      
      // If no categories exist, create default ones
      if (!data || data.length === 0) {
        console.log('No company expense categories found, creating defaults...')
        return await this.createDefaultCompanyCategories()
      }
      
      return data || []
    } catch (error) {
      console.error('Error fetching company expense categories:', error)
      // Return default categories as fallback
      return this.getDefaultCategories()
    }
  }

  // Create default company expense categories
  static async createDefaultCompanyCategories(): Promise<CompanyExpenseCategory[]> {
    try {
      const defaultCategories = this.getDefaultCategories()

      // Try to create the categories in the database
      try {
        const { data, error } = await supabase
          .from('company_expense_categories')
          .insert(defaultCategories.map(cat => ({
            name: cat.name,
            description: cat.description,
            color: cat.color,
            icon: cat.icon,
            is_project_related: cat.is_project_related,
            is_active: true
          })))
          .select()

        if (error) {
          // If table doesn't exist, just return defaults
          if (error.code === '42P01') {
            console.log('Table does not exist, using default categories')
            return defaultCategories
          }
          throw error
        }
        console.log('Default company expense categories created successfully')
        return data || defaultCategories
      } catch (dbError) {
        console.warn('Could not create categories in database, using defaults:', dbError)
        return defaultCategories
      }
    } catch (error) {
      console.error('Error creating default company categories:', error)
      return this.getDefaultCategories()
    }
  }

  // Get default categories (fallback)
  static getDefaultCategories(): CompanyExpenseCategory[] {
    return [
      {
        id: '550e8400-e29b-41d4-a716-446655440001',
        name: 'Office Supplies',
        description: 'Stationery, equipment, and office materials',
        color: '#3B82F6',
        icon: '📎',
        is_project_related: false,
        is_active: true,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      },
      {
        id: '550e8400-e29b-41d4-a716-446655440002',
        name: 'Equipment & Tools',
        description: 'Construction tools, machinery, and equipment',
        color: '#F59E0B',
        icon: '🔧',
        is_project_related: true,
        is_active: true,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      },
      {
        id: '550e8400-e29b-41d4-a716-446655440003',
        name: 'Materials & Supplies',
        description: 'Construction materials and project supplies',
        color: '#10B981',
        icon: '🧱',
        is_project_related: true,
        is_active: true,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      },
      {
        id: '550e8400-e29b-41d4-a716-446655440004',
        name: 'Utilities',
        description: 'Electricity, water, internet, and other utilities',
        color: '#EF4444',
        icon: '💡',
        is_project_related: false,
        is_active: true,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      },
      {
        id: '550e8400-e29b-41d4-a716-************',
        name: 'Transportation',
        description: 'Vehicle expenses, fuel, and transportation costs',
        color: '#8B5CF6',
        icon: '🚗',
        is_project_related: false,
        is_active: true,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      },
      {
        id: '550e8400-e29b-41d4-a716-************',
        name: 'Professional Services',
        description: 'Legal, accounting, consulting, and other professional services',
        color: '#06B6D4',
        icon: '💼',
        is_project_related: false,
        is_active: true,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      },
      {
        id: '550e8400-e29b-41d4-a716-************',
        name: 'Marketing & Advertising',
        description: 'Marketing campaigns, advertising, and promotional materials',
        color: '#EC4899',
        icon: '📢',
        is_project_related: false,
        is_active: true,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      },
      {
        id: '550e8400-e29b-41d4-a716-************',
        name: 'Insurance',
        description: 'Business insurance, liability, and coverage costs',
        color: '#84CC16',
        icon: '🛡️',
        is_project_related: false,
        is_active: true,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      },
      {
        id: '550e8400-e29b-41d4-a716-446655440009',
        name: 'Maintenance & Repairs',
        description: 'Equipment maintenance, repairs, and upkeep',
        color: '#F97316',
        icon: '🔨',
        is_project_related: true,
        is_active: true,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      },
      {
        id: '550e8400-e29b-41d4-a716-446655440010',
        name: 'Other Expenses',
        description: 'Miscellaneous business expenses',
        color: '#6B7280',
        icon: '📦',
        is_project_related: false,
        is_active: true,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      }
    ]
  }

  static async createCompanyExpenseCategory(categoryData: Omit<CompanyExpenseCategory, 'id' | 'created_at' | 'updated_at'>): Promise<CompanyExpenseCategory> {
    try {
      const { data, error } = await supabase
        .from('company_expense_categories')
        .insert([categoryData])
        .select()
        .single()

      if (error) {
        // If table doesn't exist, create a mock category
        if (error.code === '42P01') {
          console.log('Table does not exist, creating mock category')
          return {
            id: `mock-${Date.now()}`,
            ...categoryData,
            created_at: new Date().toISOString(),
            updated_at: new Date().toISOString()
          }
        }
        throw error
      }
      return data
    } catch (error) {
      console.error('Error creating company expense category:', error)
      throw error
    }
  }

  static async updateCompanyExpenseCategory(categoryId: string, categoryData: Partial<CompanyExpenseCategory>): Promise<CompanyExpenseCategory> {
    try {
      const { data, error } = await supabase
        .from('company_expense_categories')
        .update({
          ...categoryData,
          updated_at: new Date().toISOString()
        })
        .eq('id', categoryId)
        .select()
        .single()

      if (error) throw error
      return data
    } catch (error) {
      console.error('Error updating company expense category:', error)
      throw error
    }
  }

  static async deleteCompanyExpenseCategory(categoryId: string): Promise<void> {
    try {
      // Soft delete by setting is_active to false
      const { error } = await supabase
        .from('company_expense_categories')
        .update({
          is_active: false,
          updated_at: new Date().toISOString()
        })
        .eq('id', categoryId)

      if (error) throw error
    } catch (error) {
      console.error('Error deleting company expense category:', error)
      throw error
    }
  }

  // Company Expenses
  static async getCompanyExpenses(): Promise<CompanyExpense[]> {
    try {
      const { data, error } = await supabase
        .from('expenses')
        .select('*')
        .order('expense_date', { ascending: false })

      if (error) {
        console.error('Error fetching expenses:', error)
        return []
      }

      // Get categories separately to avoid foreign key issues
      const categories = await this.getCompanyExpenseCategories()

      // Merge expenses with categories
      const expensesWithCategories = (data || []).map(expense => ({
        ...expense,
        category: categories.find(cat => cat.id === expense.category_id)
      }))

      return expensesWithCategories
    } catch (error) {
      console.error('Error fetching company expenses:', error)
      return []
    }
  }

  static async createCompanyExpense(expenseData: Omit<CompanyExpense, 'id' | 'created_at' | 'updated_at'>): Promise<CompanyExpense> {
    try {
      console.log('Creating company expense with data:', expenseData)

      // Validate category_id
      if (!expenseData.category_id) {
        throw new Error('Category ID is required')
      }

      // Validate that category exists
      const categories = await this.getCompanyExpenseCategories()
      console.log('Available categories:', categories.map(c => ({ id: c.id, name: c.name })))

      const categoryExists = categories.some(cat => cat.id === expenseData.category_id)

      if (!categoryExists) {
        console.warn(`Category ${expenseData.category_id} not found, using first available category`)
        // Use the first available category as fallback
        expenseData.category_id = categories.length > 0 ? categories[0].id : '550e8400-e29b-41d4-a716-446655440010'
        console.log('Using fallback category:', expenseData.category_id)
      }

      // Only include fields that exist in the expenses table
      const cleanExpenseData = {
        category_id: expenseData.category_id,
        description: expenseData.description,
        amount: expenseData.amount,
        expense_date: expenseData.expense_date,
        payment_method: expenseData.payment_method,
        vendor_name: expenseData.vendor_name,
        status: expenseData.status,
        is_recurring: expenseData.is_recurring,
        recurring_frequency: expenseData.recurring_frequency
        // Exclude metadata, project_id, receipt_url if they don't exist in the table
      }

      const { data, error } = await supabase
        .from('expenses')
        .insert([cleanExpenseData])
        .select()
        .single()

      if (error) throw error

      // Get the category separately
      const categories2 = await this.getCompanyExpenseCategories()
      const category = categories2.find(cat => cat.id === data.category_id)

      return {
        ...data,
        category,
        // Add missing fields with default values
        project_id: expenseData.project_id || undefined,
        receipt_url: expenseData.receipt_url || undefined,
        metadata: expenseData.metadata || undefined
      }
    } catch (error) {
      console.error('Error creating company expense:', error)
      throw error
    }
  }

  static async updateCompanyExpense(expenseId: string, expenseData: Partial<CompanyExpense>): Promise<CompanyExpense> {
    try {
      // Only include fields that exist in the expenses table
      const cleanExpenseData: any = {}

      if (expenseData.category_id !== undefined) cleanExpenseData.category_id = expenseData.category_id
      if (expenseData.description !== undefined) cleanExpenseData.description = expenseData.description
      if (expenseData.amount !== undefined) cleanExpenseData.amount = expenseData.amount
      if (expenseData.expense_date !== undefined) cleanExpenseData.expense_date = expenseData.expense_date
      if (expenseData.payment_method !== undefined) cleanExpenseData.payment_method = expenseData.payment_method
      if (expenseData.vendor_name !== undefined) cleanExpenseData.vendor_name = expenseData.vendor_name
      if (expenseData.status !== undefined) cleanExpenseData.status = expenseData.status
      if (expenseData.is_recurring !== undefined) cleanExpenseData.is_recurring = expenseData.is_recurring
      if (expenseData.recurring_frequency !== undefined) cleanExpenseData.recurring_frequency = expenseData.recurring_frequency

      const { data, error } = await supabase
        .from('expenses')
        .update(cleanExpenseData)
        .eq('id', expenseId)
        .select()
        .single()

      if (error) throw error

      // Get the category separately
      const categories = await this.getCompanyExpenseCategories()
      const category = categories.find(cat => cat.id === data.category_id)

      return {
        ...data,
        category,
        // Add missing fields with default values
        project_id: expenseData.project_id || data.project_id || undefined,
        receipt_url: expenseData.receipt_url || data.receipt_url || undefined,
        metadata: expenseData.metadata || data.metadata || undefined
      }
    } catch (error) {
      console.error('Error updating company expense:', error)
      throw error
    }
  }

  static async deleteCompanyExpense(expenseId: string): Promise<void> {
    try {
      const { error } = await supabase
        .from('expenses')
        .delete()
        .eq('id', expenseId)

      if (error) throw error
    } catch (error) {
      console.error('Error deleting company expense:', error)
      throw error
    }
  }
}
