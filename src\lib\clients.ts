import { supabase } from './supabase';
import { generateUUID } from './utils';
import {
  Client,
  ClientPayment,
  ClientInvoice,
  ClientFinancialSummary,
  ClientContact,
  ClientDocument,
  ClientActivity,
  ClientFilters,
  ClientAnalytics
} from '@/types/client';

export class ClientService {
  // Validation helper for client data
  static validateClientData(clientData: Partial<Client>): { isValid: boolean; errors: string[] } {
    const errors: string[] = [];

    // Required field validation
    if (!clientData.name || clientData.name.trim().length === 0) {
      errors.push('Client name is required');
    }

    // Email validation (if provided)
    if (clientData.email && clientData.email.trim().length > 0) {
      const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
      if (!emailRegex.test(clientData.email)) {
        errors.push('Valid email address is required');
      }
    }

    // Phone validation (if provided)
    if (clientData.phone && clientData.phone.trim().length > 0) {
      const phoneRegex = /^[\+]?[1-9][\d]{0,15}$/;
      if (!phoneRegex.test(clientData.phone.replace(/[\s\-\(\)]/g, ''))) {
        errors.push('Valid phone number is required');
      }
    }

    // Payment terms validation
    if (clientData.payment_terms && (clientData.payment_terms < 0 || clientData.payment_terms > 365)) {
      errors.push('Payment terms must be between 0 and 365 days');
    }

    // Credit limit validation
    if (clientData.credit_limit && clientData.credit_limit < 0) {
      errors.push('Credit limit cannot be negative');
    }

    return {
      isValid: errors.length === 0,
      errors
    };
  }

  // Check for duplicate clients
  static async checkDuplicateClient(name: string, email?: string, excludeId?: string): Promise<boolean> {
    try {
      let query = supabase
        .from('clients')
        .select('id, name, email');

      // Check for duplicate name or email
      if (email && email.trim().length > 0) {
        query = query.or(`name.eq.${name.trim()},email.eq.${email.trim()}`);
      } else {
        query = query.eq('name', name.trim());
      }

      if (excludeId) {
        query = query.neq('id', excludeId);
      }

      const { data, error } = await query;

      if (error) {
        console.error('Error checking duplicate client:', error);
        return false;
      }

      return (data && data.length > 0);
    } catch (error) {
      console.error('Error in checkDuplicateClient:', error);
      return false;
    }
  }

  // Client CRUD operations
  static async getClients(filters?: ClientFilters): Promise<Client[]> {
    try {
      console.log('Fetching clients from Supabase...');
      
      let query = supabase.from('clients').select('*');
      
      if (filters?.status) {
        query = query.eq('status', filters.status);
      }
      
      if (filters?.client_type) {
        query = query.eq('client_type', filters.client_type);
      }
      
      if (filters?.industry) {
        query = query.eq('industry', filters.industry);
      }
      
      if (filters?.search) {
        query = query.or(`name.ilike.%${filters.search}%,company_name.ilike.%${filters.search}%,email.ilike.%${filters.search}%`);
      }
      
      const { data, error } = await query.order('created_at', { ascending: false });
      
      if (error) {
        console.error('Error fetching clients:', error);
        // No fallback - return empty array
        return [];
      }
      
      console.log('Successfully fetched clients:', data?.length || 0);
      return data || [];
    } catch (error) {
      console.error('Error in getClients:', error);
      return [];
    }
  }

  static async getClientById(id: string): Promise<Client | null> {
    try {
      const { data, error } = await supabase
        .from('clients')
        .select('*')
        .eq('id', id)
        .single();
      
      if (error) {
        console.error('Error fetching client:', error);
        return null;
      }
      
      return data;
    } catch (error) {
      console.error('Error in getClientById:', error);
      return null;
    }
  }

  static async createClient(client: Omit<Client, 'id' | 'created_at' | 'updated_at'>): Promise<Client> {
    try {
      console.log('Creating client:', client);

      // Validate client data
      const validation = this.validateClientData(client);
      if (!validation.isValid) {
        throw new Error(`Validation failed: ${validation.errors.join(', ')}`);
      }

      // Check for duplicates
      const isDuplicate = await this.checkDuplicateClient(client.name, client.email);
      if (isDuplicate) {
        throw new Error(`A client with name "${client.name}"${client.email ? ` or email "${client.email}"` : ''} already exists`);
      }

      // Clean and prepare data
      const cleanedClient = {
        ...client,
        name: client.name.trim(),
        email: client.email?.trim() || undefined,
        phone: client.phone?.trim() || undefined,
        company_name: client.company_name?.trim() || undefined,
        address: client.address?.trim() || undefined,
        city: client.city?.trim() || undefined,
        state: client.state?.trim() || undefined,
        zip_code: client.zip_code?.trim() || undefined,
        contact_person: client.contact_person?.trim() || undefined,
        tax_id: client.tax_id?.trim() || undefined,
        industry: client.industry?.trim() || undefined,
        website: client.website?.trim() || undefined,
        notes: client.notes?.trim() || undefined
      };

      const { data, error } = await supabase
        .from('clients')
        .insert([cleanedClient])
        .select()
        .single();

      if (error) {
        console.error('Error creating client:', error);
        throw error;
      }

      console.log('Client created successfully:', data);
      return data;
    } catch (error) {
      console.error('Error in createClient:', error);
      throw error;
    }
  }

  static async updateClient(id: string, updates: Partial<Client>): Promise<Client> {
    try {
      const updatedClient = {
        ...updates,
        updated_at: new Date().toISOString()
      };

      const { data, error } = await supabase
        .from('clients')
        .update(updatedClient)
        .eq('id', id)
        .select()
        .single();

      if (error) {
        console.error('Error updating client:', error);
        throw error;
      }

      console.log('Client updated successfully:', data);
      return data;
    } catch (error) {
      console.error('Error in updateClient:', error);
      throw error;
    }
  }

  static async deleteClient(id: string): Promise<void> {
    try {
      const { error } = await supabase
        .from('clients')
        .delete()
        .eq('id', id);

      if (error) {
        console.error('Error deleting client:', error);
        throw error;
      }

      console.log('Client deleted successfully');
    } catch (error) {
      console.error('Error in deleteClient:', error);
      throw error;
    }
  }

  // Financial operations
  static async getClientFinancialSummary(clientId: string): Promise<ClientFinancialSummary> {
    try {
      // Get the client and their actual financial data
      const client = await this.getClientById(clientId);
      if (!client) {
        throw new Error('Client not found');
      }

      // Get real invoices and payments
      const invoices = await this.getClientInvoices(clientId);
      const payments = await this.getClientPayments(clientId);

      // Calculate real financial metrics
      const totalInvoiced = invoices.reduce((sum, inv) => sum + inv.total_amount, 0);
      const totalPaid = payments.reduce((sum, pay) => sum + pay.amount, 0);
      const outstandingAmount = totalInvoiced - totalPaid;
      const overdueAmount = invoices
        .filter(inv => inv.status === 'Overdue')
        .reduce((sum, inv) => sum + inv.total_amount, 0);

      // Get last payment info
      const lastPayment = payments.sort((a, b) =>
        new Date(b.payment_date).getTime() - new Date(a.payment_date).getTime()
      )[0];

      // Calculate average payment days
      const paidInvoices = invoices.filter(inv => inv.paid_date);
      const averagePaymentDays = paidInvoices.length > 0
        ? Math.round(paidInvoices.reduce((sum, inv) => {
            const issueDate = new Date(inv.issue_date);
            const paidDate = new Date(inv.paid_date!);
            const daysDiff = Math.floor((paidDate.getTime() - issueDate.getTime()) / (1000 * 60 * 60 * 24));
            return sum + daysDiff;
          }, 0) / paidInvoices.length)
        : client.payment_terms || 30;

      const creditLimit = client.credit_limit || 0;
      const creditUsed = Math.min(outstandingAmount, creditLimit);

      return {
        client_id: clientId,
        total_invoiced: totalInvoiced,
        total_paid: totalPaid,
        outstanding_amount: outstandingAmount,
        overdue_amount: overdueAmount,
        credit_used: creditUsed,
        credit_available: Math.max(0, creditLimit - creditUsed),
        last_payment_date: lastPayment?.payment_date || null,
        last_payment_amount: lastPayment?.amount || 0,
        average_payment_days: averagePaymentDays,
        payment_history_months: 12,
        total_projects: new Set(invoices.map(inv => inv.project_id).filter(Boolean)).size,
        active_projects: invoices.filter(inv => inv.status !== 'Paid' && inv.status !== 'Cancelled').length
      };
    } catch (error) {
      console.error('Error getting client financial summary:', error);
      throw error;
    }
  }

  static async recordPayment(paymentData: Omit<ClientPayment, 'id' | 'created_at' | 'updated_at' | 'currency'>): Promise<ClientPayment | null> {
    try {
      console.log('Recording payment:', paymentData);

      const payment: ClientPayment = {
        ...paymentData,
        id: generateUUID(),
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
        currency: 'USD'
      };

      // Try to save to Supabase first
      const { data, error } = await supabase
        .from('client_payments')
        .insert([payment])
        .select()
        .single();

      if (error) {
        console.error('Error saving payment to Supabase:', error);
        throw error;
      }

      console.log('Payment saved to Supabase:', data);
      return data;
    } catch (error) {
      console.error('Error recording payment:', error);
      return null;
    }
  }

  static async getClientPayments(clientId: string): Promise<ClientPayment[]> {
    try {
      const { data, error } = await supabase
        .from('client_payments')
        .select('*')
        .eq('client_id', clientId)
        .order('payment_date', { ascending: false });

      if (error) {
        console.error('Error fetching client payments:', error);
        return [];
      }

      return data || [];
    } catch (error) {
      console.error('Error in getClientPayments:', error);
      return [];
    }
  }

  static async getClientInvoices(clientId: string): Promise<ClientInvoice[]> {
    try {
      // Fetch invoices from documents table using client_id foreign key
      const { data, error } = await supabase
        .from('documents')
        .select('*')
        .eq('client_id', clientId)
        .eq('type', 'invoice')
        .order('issue_date', { ascending: false });

      if (error) {
        console.error('Error fetching client invoices from documents:', error);
        // Fallback: try to match by client name and email if client_id lookup fails
        const client = await this.getClientById(clientId);
        if (client) {
          const { data: fallbackData, error: fallbackError } = await supabase
            .from('documents')
            .select('*')
            .eq('type', 'invoice')
            .eq('client_name', client.name)
            .eq('client_email', client.email)
            .order('issue_date', { ascending: false });

          if (!fallbackError && fallbackData) {
            // Convert documents to ClientInvoice format
            return fallbackData.map((doc: any) => ({
              id: doc.id,
              client_id: clientId,
              invoice_number: doc.document_number,
              description: doc.title,
              issue_date: doc.issue_date,
              due_date: doc.due_date,
              total_amount: doc.total_amount,
              status: doc.status,
              project_name: doc.project_name,
              items: doc.items || []
            }));
          }
        }

        // No fallback - return empty array
        return [];
      }

      // Convert documents to ClientInvoice format
      const invoices = (data || []).map((doc: any) => ({
        id: doc.id,
        client_id: clientId,
        invoice_number: doc.document_number,
        description: doc.title,
        issue_date: doc.issue_date,
        due_date: doc.due_date,
        total_amount: doc.total_amount,
        status: doc.status,
        project_name: doc.project_name,
        items: doc.items || []
      }));

      return invoices;
    } catch (error) {
      console.error('Error in getClientInvoices:', error);
      return [];
    }
  }

  // Analytics - using real database data only
  static async getClientAnalytics(): Promise<ClientAnalytics> {
    try {
      // Get real data from database only
      const clients = await this.getClients();

      // Get all invoices from documents table
      const { data: invoiceDocuments, error: invoiceError } = await supabase
        .from('documents')
        .select('*')
        .eq('type', 'invoice');

      if (invoiceError) {
        console.error('Error fetching invoices for analytics:', invoiceError);
      }

      // Get all payments from client_payments table
      const { data: paymentData, error: paymentError } = await supabase
        .from('client_payments')
        .select('*');

      if (paymentError) {
        console.error('Error fetching payments for analytics:', paymentError);
      }

      const allInvoices = invoiceDocuments || [];
      const allPayments = paymentData || [];

      // Calculate real analytics from database data
      const totalRevenue = allInvoices.reduce((sum: number, inv: any) => {
        // For documents table, use amount field or parse from content
        const amount = inv.amount || inv.total_amount || 0;
        return sum + amount;
      }, 0);

      const totalPaid = allPayments.reduce((sum: number, pay: any) => sum + (pay.amount || 0), 0);
      const outstandingRevenue = totalRevenue - totalPaid;

      const overdueRevenue = allInvoices
        .filter((inv: any) => inv.status === 'Overdue' || inv.status === 'overdue')
        .reduce((sum: number, inv: any) => {
          const amount = inv.amount || inv.total_amount || 0;
          return sum + amount;
        }, 0);

      // Calculate average payment time
      const paidInvoices = allInvoices.filter((inv: any) => inv.paid_date);
      const averagePaymentTime = paidInvoices.length > 0
        ? Math.round(paidInvoices.reduce((sum: number, inv: any) => {
            const issueDate = new Date(inv.issue_date);
            const paidDate = new Date(inv.paid_date);
            const daysDiff = Math.floor((paidDate.getTime() - issueDate.getTime()) / (1000 * 60 * 60 * 24));
            return sum + daysDiff;
          }, 0) / paidInvoices.length)
        : 30;

      // Calculate top clients by revenue
      const clientRevenue = new Map();
      allInvoices.forEach((inv: any) => {
        const current = clientRevenue.get(inv.client_id) || 0;
        clientRevenue.set(inv.client_id, current + inv.total_amount);
      });

      const topClients = Array.from(clientRevenue.entries())
        .map(([clientId, revenue]) => {
          const client = clients.find(c => c.id === clientId);
          const projectCount = new Set(allInvoices.filter((inv: any) => inv.client_id === clientId).map((inv: any) => inv.project_id)).size;
          return {
            client_id: clientId,
            client_name: client?.name || 'Unknown Client',
            total_revenue: revenue as number,
            project_count: projectCount
          };
        })
        .sort((a, b) => b.total_revenue - a.total_revenue)
        .slice(0, 5);

      // Calculate payment trends (last 3 months)
      const paymentTrends = [];
      for (let i = 2; i >= 0; i--) {
        const date = new Date();
        date.setMonth(date.getMonth() - i);
        const monthKey = `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}`;

        const monthPayments = allPayments.filter((pay: any) => pay.payment_date.startsWith(monthKey));
        const monthInvoices = allInvoices.filter((inv: any) => inv.issue_date.startsWith(monthKey));

        const paymentsReceived = monthPayments.reduce((sum: number, pay: any) => sum + pay.amount, 0);
        const invoicesSent = monthInvoices.reduce((sum: number, inv: any) => sum + inv.total_amount, 0);
        const outstanding = invoicesSent - paymentsReceived;

        paymentTrends.push({
          month: monthKey,
          payments_received: paymentsReceived,
          invoices_sent: invoicesSent,
          outstanding: Math.max(0, outstanding)
        });
      }

      // Calculate industry breakdown
      const industryMap = new Map();
      clients.forEach(client => {
        const industry = client.industry || 'Other';
        const current = industryMap.get(industry) || { client_count: 0, revenue: 0 };

        const clientRevenue = allInvoices
          .filter((inv: any) => inv.client_id === client.id)
          .reduce((sum: number, inv: any) => sum + inv.total_amount, 0);

        industryMap.set(industry, {
          client_count: current.client_count + 1,
          revenue: current.revenue + clientRevenue
        });
      });

      const industryBreakdown = Array.from(industryMap.entries())
        .map(([industry, data]) => ({
          industry,
          client_count: data.client_count,
          revenue: data.revenue
        }))
        .sort((a, b) => b.revenue - a.revenue);

      return {
        total_clients: clients.length,
        active_clients: clients.filter(c => c.status === 'Active').length,
        total_revenue: totalRevenue,
        outstanding_revenue: outstandingRevenue,
        overdue_revenue: overdueRevenue,
        average_payment_time: averagePaymentTime,
        top_clients: topClients,
        payment_trends: paymentTrends,
        industry_breakdown: industryBreakdown
      };
    } catch (error) {
      console.error('Error getting client analytics:', error);
      throw error;
    }
  }

  // Initialize financial data for all existing clients (removed - using Supabase only)
  static async initializeFinancialDataForExistingClients(): Promise<void> {
    console.log('Financial data initialization disabled - using Supabase only');
  }

  // Sample financial data creation removed - using Supabase only

  // Mock data removed - using Supabase only

  // Mock payments removed - using Supabase only

  // Mock invoices removed - using Supabase only
}
