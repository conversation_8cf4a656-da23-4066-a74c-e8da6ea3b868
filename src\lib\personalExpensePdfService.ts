import jsPDF from 'jspdf'
import 'jspdf-autotable'
import { ExpenseReport, CategoryBreakdown, BudgetBreakdown } from './personalExpenseReportService'
import { PersonalExpense } from './personalExpenseService'

// Extend jsPDF type to include autoTable
declare module 'jspdf' {
  interface jsPDF {
    autoTable: (options: any) => jsPDF
  }
}

export class PersonalExpensePdfService {
  // Generate comprehensive PDF report
  static generateExpenseReportPdf(report: ExpenseReport, userInfo?: { name?: string; email?: string }): void {
    console.log('Generating comprehensive PDF with report:', report)

    if (!report) {
      throw new Error('No report data provided for PDF generation')
    }

    const doc = new jsPDF()
    let yPosition = 20

    // Add header
    yPosition = this.addHeader(doc, yPosition, userInfo)
    
    // Add report title and period
    yPosition = this.addReportTitle(doc, yPosition, report.period)
    
    // Add executive summary
    yPosition = this.addExecutiveSummary(doc, yPosition, report)
    
    // Add budget overview
    yPosition = this.addBudgetOverview(doc, yPosition, report)
    
    // Check if we need a new page
    if (yPosition > 200) {
      doc.addPage()
      yPosition = 20
    }
    
    // Add category breakdown
    yPosition = this.addCategoryBreakdown(doc, yPosition, report.categoryBreakdown)
    
    // Add budget breakdown
    if (yPosition > 150) {
      doc.addPage()
      yPosition = 20
    }
    yPosition = this.addBudgetBreakdown(doc, yPosition, report.budgetBreakdown)
    
    // Add top expenses
    if (yPosition > 150) {
      doc.addPage()
      yPosition = 20
    }
    yPosition = this.addTopExpenses(doc, yPosition, report.topExpenses)
    
    // Add recommendations
    if (yPosition > 200) {
      doc.addPage()
      yPosition = 20
    }
    yPosition = this.addRecommendations(doc, yPosition, report.summary.recommendations)
    
    // Add footer
    this.addFooter(doc)
    
    // Save the PDF
    const fileName = `Personal_Expense_Report_${new Date().toISOString().split('T')[0]}.pdf`
    doc.save(fileName)
  }

  // Generate budget utilization PDF
  static generateBudgetUtilizationPdf(budgetBreakdown: BudgetBreakdown[], userInfo?: { name?: string; email?: string }): void {
    console.log('Generating budget utilization PDF with data:', budgetBreakdown)

    if (!budgetBreakdown || budgetBreakdown.length === 0) {
      console.warn('No budget breakdown data provided for PDF generation')
      throw new Error('No budget data available for PDF generation')
    }

    const doc = new jsPDF()
    let yPosition = 20

    // Add header
    yPosition = this.addHeader(doc, yPosition, userInfo)
    
    // Add title
    doc.setFontSize(18)
    doc.setFont('helvetica', 'bold')
    doc.text('Budget Utilization Report', 20, yPosition)
    yPosition += 15

    doc.setFontSize(12)
    doc.setFont('helvetica', 'normal')
    doc.text(`Generated on: ${new Date().toLocaleDateString()}`, 20, yPosition)
    yPosition += 20

    // Add budget breakdown table
    const budgetTableData = budgetBreakdown.map(budgetItem => {
      // Handle both budget.budget.name and budget.name structures
      const budget = budgetItem.budget || budgetItem
      const name = budget.name || 'Unknown Budget'
      const amount = budget.amount || 0

      return [
        name,
        `$${amount.toFixed(2)}`,
        `$${(budgetItem.totalSpent || 0).toFixed(2)}`,
        `$${(budgetItem.remainingAmount || 0).toFixed(2)}`,
        `${(budgetItem.utilizationPercentage || 0).toFixed(1)}%`,
        budgetItem.isOverBudget ? 'Over Budget' : 'On Track'
      ]
    })

    doc.autoTable({
      startY: yPosition,
      head: [['Budget Name', 'Allocated', 'Spent', 'Remaining', 'Utilization', 'Status']],
      body: budgetTableData,
      theme: 'grid',
      headStyles: { fillColor: [41, 128, 185] },
      styles: { fontSize: 10 },
      columnStyles: {
        5: { 
          cellWidth: 25,
          halign: 'center',
          textColor: (data: any) => {
            const budget = budgetBreakdown[data.row.index]
            return budget.isOverBudget ? [231, 76, 60] : [39, 174, 96]
          }
        }
      }
    })

    // Add budget details for each budget
    yPosition = (doc as any).lastAutoTable.finalY + 20

    budgetBreakdown.forEach((budget, index) => {
      if (yPosition > 220) {
        doc.addPage()
        yPosition = 20
      }

      // Budget details section
      const budgetInfo = budgetItem.budget || budgetItem
      const name = budgetInfo.name || 'Unknown Budget'
      const startDate = budgetInfo.start_date || 'N/A'
      const endDate = budgetInfo.end_date || 'N/A'

      doc.setFontSize(14)
      doc.setFont('helvetica', 'bold')
      doc.text(`${name} - Detailed Breakdown`, 20, yPosition)
      yPosition += 10

      doc.setFontSize(10)
      doc.setFont('helvetica', 'normal')
      doc.text(`Period: ${startDate} to ${endDate}`, 20, yPosition)
      yPosition += 5
      doc.text(`Days Remaining: ${budgetItem.daysRemaining || 0}`, 20, yPosition)
      yPosition += 5
      doc.text(`Daily Budget Remaining: $${(budgetItem.dailyBudgetRemaining || 0).toFixed(2)}`, 20, yPosition)
      yPosition += 15

      // Expenses for this budget
      if (budget.expenses.length > 0) {
        const expenseTableData = budget.expenses.slice(0, 10).map(expense => [
          expense.expense_date,
          expense.title,
          expense.category?.name || 'Uncategorized',
          `$${expense.amount.toFixed(2)}`
        ])

        doc.autoTable({
          startY: yPosition,
          head: [['Date', 'Description', 'Category', 'Amount']],
          body: expenseTableData,
          theme: 'striped',
          styles: { fontSize: 9 },
          headStyles: { fillColor: [52, 152, 219] }
        })

        yPosition = (doc as any).lastAutoTable.finalY + 15
      } else {
        doc.text('No expenses recorded for this budget.', 20, yPosition)
        yPosition += 15
      }
    })

    // Add footer
    this.addFooter(doc)
    
    // Save the PDF
    const fileName = `Budget_Utilization_Report_${new Date().toISOString().split('T')[0]}.pdf`
    doc.save(fileName)
  }

  // Generate expense list PDF
  static generateExpenseListPdf(
    expenses: PersonalExpense[], 
    title: string = 'Expense List',
    userInfo?: { name?: string; email?: string }
  ): void {
    const doc = new jsPDF()
    let yPosition = 20

    // Add header
    yPosition = this.addHeader(doc, yPosition, userInfo)
    
    // Add title
    doc.setFontSize(18)
    doc.setFont('helvetica', 'bold')
    doc.text(title, 20, yPosition)
    yPosition += 15

    doc.setFontSize(12)
    doc.setFont('helvetica', 'normal')
    doc.text(`Generated on: ${new Date().toLocaleDateString()}`, 20, yPosition)
    doc.text(`Total Expenses: ${expenses.length}`, 120, yPosition)
    yPosition += 5

    const totalAmount = expenses.reduce((sum, expense) => sum + expense.amount, 0)
    doc.text(`Total Amount: $${totalAmount.toFixed(2)}`, 20, yPosition)
    yPosition += 20

    // Create expense table
    const expenseTableData = expenses.map(expense => [
      expense.expense_date,
      expense.title,
      expense.category?.name || 'Uncategorized',
      expense.budget?.name || 'No Budget',
      `$${expense.amount.toFixed(2)}`,
      expense.payment_method || 'N/A'
    ])

    doc.autoTable({
      startY: yPosition,
      head: [['Date', 'Description', 'Category', 'Budget', 'Amount', 'Payment Method']],
      body: expenseTableData,
      theme: 'striped',
      headStyles: { fillColor: [41, 128, 185] },
      styles: { fontSize: 9 },
      columnStyles: {
        4: { halign: 'right' }
      }
    })

    // Add footer
    this.addFooter(doc)
    
    // Save the PDF
    const fileName = `${title.replace(/\s+/g, '_')}_${new Date().toISOString().split('T')[0]}.pdf`
    doc.save(fileName)
  }

  // Helper methods for PDF sections
  private static addHeader(doc: jsPDF, yPosition: number, userInfo?: { name?: string; email?: string }): number {
    doc.setFontSize(16)
    doc.setFont('helvetica', 'bold')
    doc.text('Personal Expense Management', 20, yPosition)
    
    if (userInfo?.name) {
      doc.setFontSize(12)
      doc.setFont('helvetica', 'normal')
      doc.text(`Prepared for: ${userInfo.name}`, 20, yPosition + 10)
      if (userInfo.email) {
        doc.text(`Email: ${userInfo.email}`, 20, yPosition + 20)
        return yPosition + 35
      }
      return yPosition + 25
    }
    
    return yPosition + 20
  }

  private static addReportTitle(doc: jsPDF, yPosition: number, period: string): number {
    doc.setFontSize(18)
    doc.setFont('helvetica', 'bold')
    doc.text('Expense Report', 20, yPosition)
    
    doc.setFontSize(12)
    doc.setFont('helvetica', 'normal')
    doc.text(`Period: ${period}`, 20, yPosition + 10)
    doc.text(`Generated: ${new Date().toLocaleDateString()}`, 20, yPosition + 20)
    
    return yPosition + 35
  }

  private static addExecutiveSummary(doc: jsPDF, yPosition: number, report: ExpenseReport): number {
    doc.setFontSize(14)
    doc.setFont('helvetica', 'bold')
    doc.text('Executive Summary', 20, yPosition)
    yPosition += 15

    doc.setFontSize(11)
    doc.setFont('helvetica', 'normal')
    
    const summaryLines = [
      `Total Expenses: $${report.totalExpenses.toFixed(2)}`,
      `Total Budget: $${report.totalBudget.toFixed(2)}`,
      `Budget Utilization: ${report.budgetUtilization.toFixed(1)}%`,
      `Average Expense: $${report.summary.averageExpenseAmount.toFixed(2)}`,
      `Budget Compliance Rate: ${report.summary.budgetComplianceRate.toFixed(1)}%`,
      `Savings Rate: ${report.summary.savingsRate.toFixed(1)}%`
    ]

    summaryLines.forEach(line => {
      doc.text(line, 20, yPosition)
      yPosition += 8
    })

    return yPosition + 10
  }

  private static addBudgetOverview(doc: jsPDF, yPosition: number, report: ExpenseReport): number {
    doc.setFontSize(14)
    doc.setFont('helvetica', 'bold')
    doc.text('Budget Overview', 20, yPosition)
    yPosition += 15

    // Create budget overview table - handle both budget structures
    const budgetData = report.budgetBreakdown.slice(0, 5).map(budgetItem => {
      // Handle both budget.budget.name and budget.name structures
      const budget = budgetItem.budget || budgetItem
      const name = budget.name || 'Unknown Budget'
      const amount = budget.amount || 0

      return [
        name,
        `$${amount.toFixed(2)}`,
        `$${(budgetItem.totalSpent || 0).toFixed(2)}`,
        `${(budgetItem.utilizationPercentage || 0).toFixed(1)}%`,
        budgetItem.isOverBudget ? 'Over' : 'OK'
      ]
    })

    doc.autoTable({
      startY: yPosition,
      head: [['Budget', 'Allocated', 'Spent', 'Utilization', 'Status']],
      body: budgetData,
      theme: 'grid',
      headStyles: { fillColor: [52, 152, 219] },
      styles: { fontSize: 10 }
    })

    return (doc as any).lastAutoTable.finalY + 15
  }

  private static addCategoryBreakdown(doc: jsPDF, yPosition: number, categoryBreakdown: CategoryBreakdown[]): number {
    doc.setFontSize(14)
    doc.setFont('helvetica', 'bold')
    doc.text('Category Breakdown', 20, yPosition)
    yPosition += 15

    const categoryData = categoryBreakdown.slice(0, 10).map(category => [
      category.category.name,
      `$${category.totalAmount.toFixed(2)}`,
      `${category.percentage.toFixed(1)}%`,
      category.expenseCount.toString(),
      `$${category.budgetAllocated.toFixed(2)}`,
      category.isOverBudget ? 'Over' : 'OK'
    ])

    doc.autoTable({
      startY: yPosition,
      head: [['Category', 'Amount', 'Percentage', 'Count', 'Budget', 'Status']],
      body: categoryData,
      theme: 'striped',
      headStyles: { fillColor: [46, 204, 113] },
      styles: { fontSize: 9 }
    })

    return (doc as any).lastAutoTable.finalY + 15
  }

  private static addBudgetBreakdown(doc: jsPDF, yPosition: number, budgetBreakdown: BudgetBreakdown[]): number {
    doc.setFontSize(14)
    doc.setFont('helvetica', 'bold')
    doc.text('Budget Performance', 20, yPosition)
    yPosition += 15

    const budgetData = budgetBreakdown.map(budgetItem => {
      // Handle both budget.budget.name and budget.name structures
      const budget = budgetItem.budget || budgetItem
      const name = budget.name || 'Unknown Budget'
      const amount = budget.amount || 0

      return [
        name,
        `$${amount.toFixed(2)}`,
        `$${(budgetItem.totalSpent || 0).toFixed(2)}`,
        `$${(budgetItem.remainingAmount || 0).toFixed(2)}`,
        `${(budgetItem.utilizationPercentage || 0).toFixed(1)}%`,
        `${budgetItem.daysRemaining || 0} days`
      ]
    })

    doc.autoTable({
      startY: yPosition,
      head: [['Budget Name', 'Allocated', 'Spent', 'Remaining', 'Utilization', 'Days Left']],
      body: budgetData,
      theme: 'grid',
      headStyles: { fillColor: [155, 89, 182] },
      styles: { fontSize: 9 }
    })

    return (doc as any).lastAutoTable.finalY + 15
  }

  private static addTopExpenses(doc: jsPDF, yPosition: number, topExpenses: PersonalExpense[]): number {
    doc.setFontSize(14)
    doc.setFont('helvetica', 'bold')
    doc.text('Top Expenses', 20, yPosition)
    yPosition += 15

    const expenseData = topExpenses.slice(0, 10).map((expense, index) => [
      (index + 1).toString(),
      expense.expense_date,
      expense.title,
      expense.category?.name || 'Uncategorized',
      `$${expense.amount.toFixed(2)}`
    ])

    doc.autoTable({
      startY: yPosition,
      head: [['Rank', 'Date', 'Description', 'Category', 'Amount']],
      body: expenseData,
      theme: 'striped',
      headStyles: { fillColor: [231, 76, 60] },
      styles: { fontSize: 9 }
    })

    return (doc as any).lastAutoTable.finalY + 15
  }

  private static addRecommendations(doc: jsPDF, yPosition: number, recommendations: string[]): number {
    doc.setFontSize(14)
    doc.setFont('helvetica', 'bold')
    doc.text('Recommendations', 20, yPosition)
    yPosition += 15

    doc.setFontSize(11)
    doc.setFont('helvetica', 'normal')

    recommendations.forEach((recommendation, index) => {
      const lines = doc.splitTextToSize(`${index + 1}. ${recommendation}`, 170)
      lines.forEach((line: string) => {
        doc.text(line, 20, yPosition)
        yPosition += 8
      })
      yPosition += 3
    })

    return yPosition + 10
  }

  private static addFooter(doc: jsPDF): void {
    const pageCount = doc.getNumberOfPages()
    
    for (let i = 1; i <= pageCount; i++) {
      doc.setPage(i)
      doc.setFontSize(8)
      doc.setFont('helvetica', 'normal')
      doc.text(
        `Page ${i} of ${pageCount} - Generated by Personal Expense Management System`,
        20,
        doc.internal.pageSize.height - 10
      )
      doc.text(
        new Date().toLocaleDateString(),
        doc.internal.pageSize.width - 40,
        doc.internal.pageSize.height - 10
      )
    }
  }
}
