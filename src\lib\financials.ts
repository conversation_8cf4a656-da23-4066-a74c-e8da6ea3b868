import { supabase } from './supabase';
import { CompanyExpenseService, CompanyExpense, CompanyExpenseCategory } from './companyExpenseService';

// Financial Interfaces
export interface ClientFinancial {
  id: string;
  client_name: string;
  client_email: string;
  total_quoted: number;
  total_invoiced: number;
  total_paid: number;
  outstanding_amount: number;
  credit_limit: number;
  payment_terms: number;
  last_payment_date?: string;
  created_at: string;
  updated_at: string;
}

export interface ExpenseCategory {
  id: string;
  name: string;
  description?: string;
  is_project_related: boolean;
  created_at: string;
}

export interface Expense {
  id: string;
  category_id: string;
  project_id?: string;
  description: string;
  amount: number;
  expense_date: string;
  payment_method?: string;
  vendor_name?: string;
  receipt_url?: string;
  is_recurring: boolean;
  recurring_frequency?: string;
  status: 'pending' | 'approved' | 'paid';
  approved_by?: string;
  created_by?: string;
  metadata?: any;
  created_at: string;
  updated_at: string;
  category?: ExpenseCategory;
}

export interface CashFlowTransaction {
  id: string;
  transaction_type: 'inflow' | 'outflow';
  category: string;
  description: string;
  amount: number;
  transaction_date: string;
  reference_id?: string;
  reference_type?: string;
  account_type: string;
  created_at: string;
}

export interface CompanyAsset {
  id: string;
  asset_name: string;
  asset_type: 'equipment' | 'vehicle' | 'property' | 'tools';
  category?: string;
  purchase_date: string;
  purchase_price: number;
  current_value?: number;
  depreciation_method: string;
  useful_life_years?: number;
  annual_depreciation?: number;
  accumulated_depreciation: number;
  location?: string;
  condition_status: 'excellent' | 'good' | 'fair' | 'poor';
  maintenance_schedule?: string;
  last_maintenance_date?: string;
  next_maintenance_date?: string;
  serial_number?: string;
  warranty_expiry?: string;
  created_at: string;
  updated_at: string;
}

export interface PaymentRecord {
  id: string;
  client_id?: string;
  invoice_id?: string;
  payment_amount: number;
  payment_date: string;
  payment_method: string;
  reference_number?: string;
  notes?: string;
  status: 'pending' | 'completed' | 'failed';
  created_at: string;
}

export interface FinancialBudget {
  id: string;
  budget_name: string;
  budget_type: 'project' | 'operational' | 'annual';
  project_id?: string;
  budget_period_start: string;
  budget_period_end: string;
  total_budget: number;
  allocated_amount: number;
  spent_amount: number;
  remaining_amount?: number;
  status: 'active' | 'completed' | 'cancelled';
  created_by?: string;
  created_at: string;
  updated_at: string;
}

// Financial Service Class
export class FinancialService {
  // Client Financials
  static async getClientFinancials(): Promise<ClientFinancial[]> {
    try {
      const { data, error } = await supabase
        .from('client_financials')
        .select('*')
        .order('updated_at', { ascending: false });

      if (error) throw error;
      return data || [];
    } catch (error) {
      console.error('Error fetching client financials:', error);
      return [];
    }
  }

  static async createClientFinancial(clientData: Partial<ClientFinancial>): Promise<ClientFinancial | null> {
    try {
      const { data, error } = await supabase
        .from('client_financials')
        .insert([clientData])
        .select()
        .single();

      if (error) throw error;
      return data;
    } catch (error) {
      console.error('Error creating client financial:', error);
      return null;
    }
  }

  static async updateClientFinancial(id: string, updates: Partial<ClientFinancial>): Promise<boolean> {
    try {
      const { error } = await supabase
        .from('client_financials')
        .update(updates)
        .eq('id', id);

      if (error) throw error;
      return true;
    } catch (error) {
      console.error('Error updating client financial:', error);
      return false;
    }
  }

  // Expenses
  static async getExpenses(): Promise<CompanyExpense[]> {
    try {
      return await CompanyExpenseService.getCompanyExpenses();
    } catch (error) {
      console.error('Error fetching expenses:', error);
      return [];
    }
  }

  static async createExpense(expenseData: Partial<CompanyExpense>): Promise<CompanyExpense | null> {
    try {
      console.log('Creating expense:', expenseData);

      const expense = await CompanyExpenseService.createCompanyExpense({
        category_id: expenseData.category_id!,
        description: expenseData.description!,
        amount: expenseData.amount!,
        expense_date: expenseData.expense_date!,
        payment_method: expenseData.payment_method || '',
        vendor_name: expenseData.vendor_name,
        status: expenseData.status || 'pending',
        is_recurring: expenseData.is_recurring || false,
        recurring_frequency: expenseData.recurring_frequency,
        project_id: expenseData.project_id,
        receipt_url: expenseData.receipt_url,
        metadata: expenseData.metadata
      });

      console.log('Expense created:', expense);

      // If expense is paid, create cash flow transaction immediately
      if (expense && expense.status === 'paid') {
        console.log('Creating cash flow transaction for paid expense');
        await this.createCashFlowTransactionFromExpense(expense);
      }

      return expense;
    } catch (error) {
      console.error('Error creating expense:', error);
      return null;
    }
  }

  // Create cash flow transaction from expense
  static async createCashFlowTransactionFromExpense(expense: any): Promise<boolean> {
    try {
      const cashFlowData = {
        transaction_type: 'outflow' as const,
        category: expense.category?.name || 'Other',
        description: expense.description,
        amount: expense.amount,
        transaction_date: expense.expense_date,
        reference_id: expense.id,
        reference_type: 'expense',
        account_type: expense.payment_method || 'cash'
      };

      console.log('Creating cash flow transaction:', cashFlowData);

      const result = await this.createCashFlowTransaction(cashFlowData);
      console.log('Cash flow transaction created:', result);

      return result !== null;
    } catch (error) {
      console.error('Error creating cash flow transaction from expense:', error);
      return false;
    }
  }

  static async updateExpense(id: string, updates: Partial<Expense>): Promise<boolean> {
    try {
      const { error } = await supabase
        .from('expenses')
        .update(updates)
        .eq('id', id);

      if (error) throw error;
      return true;
    } catch (error) {
      console.error('Error updating expense:', error);
      return false;
    }
  }

  static async deleteExpense(id: string): Promise<boolean> {
    try {
      const { error } = await supabase
        .from('expenses')
        .delete()
        .eq('id', id);

      if (error) throw error;
      return true;
    } catch (error) {
      console.error('Error deleting expense:', error);
      return false;
    }
  }

  // Expense Categories
  static async getExpenseCategories(): Promise<CompanyExpenseCategory[]> {
    try {
      return await CompanyExpenseService.getCompanyExpenseCategories();
    } catch (error) {
      console.error('Error fetching expense categories:', error);
      return [];
    }
  }

  // Cash Flow Transactions
  static async getCashFlowTransactions(startDate?: string, endDate?: string): Promise<CashFlowTransaction[]> {
    try {
      let query = supabase
        .from('cash_flow_transactions')
        .select('*')
        .order('transaction_date', { ascending: false });

      if (startDate) {
        query = query.gte('transaction_date', startDate);
      }
      if (endDate) {
        query = query.lte('transaction_date', endDate);
      }

      const { data, error } = await query;

      if (error) throw error;
      return data || [];
    } catch (error) {
      console.error('Error fetching cash flow transactions:', error);
      return [];
    }
  }

  static async createCashFlowTransaction(transactionData: Partial<CashFlowTransaction>): Promise<CashFlowTransaction | null> {
    try {
      const { data, error } = await supabase
        .from('cash_flow_transactions')
        .insert([transactionData])
        .select()
        .single();

      if (error) throw error;
      return data;
    } catch (error) {
      console.error('Error creating cash flow transaction:', error);
      return null;
    }
  }

  // Company Assets
  static async getCompanyAssets(): Promise<CompanyAsset[]> {
    try {
      const { data, error } = await supabase
        .from('company_assets')
        .select('*')
        .order('purchase_date', { ascending: false });

      if (error) throw error;
      return data || [];
    } catch (error) {
      console.error('Error fetching company assets:', error);
      return [];
    }
  }

  static async createCompanyAsset(assetData: Partial<CompanyAsset>): Promise<CompanyAsset | null> {
    try {
      const { data, error } = await supabase
        .from('company_assets')
        .insert([assetData])
        .select()
        .single();

      if (error) throw error;
      return data;
    } catch (error) {
      console.error('Error creating company asset:', error);
      return null;
    }
  }

  static async updateCompanyAsset(id: string, updates: Partial<CompanyAsset>): Promise<boolean> {
    try {
      const { error } = await supabase
        .from('company_assets')
        .update(updates)
        .eq('id', id);

      if (error) throw error;
      return true;
    } catch (error) {
      console.error('Error updating company asset:', error);
      return false;
    }
  }

  static async deleteCompanyAsset(id: string): Promise<boolean> {
    try {
      const { error } = await supabase
        .from('company_assets')
        .delete()
        .eq('id', id);

      if (error) throw error;
      return true;
    } catch (error) {
      console.error('Error deleting company asset:', error);
      return false;
    }
  }

  // Payment Records
  static async getPaymentRecords(): Promise<PaymentRecord[]> {
    try {
      const { data, error } = await supabase
        .from('payment_records')
        .select('*')
        .order('payment_date', { ascending: false });

      if (error) throw error;
      return data || [];
    } catch (error) {
      console.error('Error fetching payment records:', error);
      return [];
    }
  }

  static async createPaymentRecord(paymentData: Partial<PaymentRecord>): Promise<PaymentRecord | null> {
    try {
      const { data, error } = await supabase
        .from('payment_records')
        .insert([paymentData])
        .select()
        .single();

      if (error) throw error;
      return data;
    } catch (error) {
      console.error('Error creating payment record:', error);
      return null;
    }
  }

  // Sync cash flow transactions from expenses and payments
  static async syncCashFlowTransactions(): Promise<boolean> {
    try {
      const { error } = await supabase.rpc('sync_cash_flow_from_transactions');
      if (error) {
        // If function doesn't exist, just log warning and return true
        if (error.message.includes('function') && error.message.includes('does not exist')) {
          console.warn('Cash flow sync function not available:', error.message);
          return true; // Don't fail the operation
        }
        throw error;
      }
      return true;
    } catch (error) {
      console.error('Error syncing cash flow transactions:', error);
      return false;
    }
  }

  // Get cash flow data for specific period
  static async getCashFlowData(
    startDate?: string,
    endDate?: string,
    periodType: 'day' | 'week' | 'month' | 'year' = 'month'
  ) {
    try {
      console.log('Fetching cash flow data with params:', { startDate, endDate, periodType });

      // First try to get data from the database function
      const { data, error } = await supabase.rpc('get_cash_flow_data', {
        start_date: startDate || new Date(Date.now() - 365 * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
        end_date: endDate || new Date().toISOString().split('T')[0],
        period_type: periodType
      });

      if (error) {
        console.warn('Database function failed, trying direct query:', error);

        // Fallback to direct query if function doesn't exist
        return await this.getCashFlowDataDirect(startDate, endDate, periodType);
      }

      console.log('Cash flow data from database:', data);
      return data || [];
    } catch (error) {
      console.error('Error getting cash flow data:', error);

      // Final fallback to direct query
      return await this.getCashFlowDataDirect(startDate, endDate, periodType);
    }
  }

  // Direct query fallback for cash flow data
  static async getCashFlowDataDirect(
    startDate?: string,
    endDate?: string,
    periodType: 'day' | 'week' | 'month' | 'year' = 'month'
  ) {
    try {
      console.log('Using direct query for cash flow data');

      let query = supabase
        .from('cash_flow_transactions')
        .select('transaction_type, amount, transaction_date');

      if (startDate) query = query.gte('transaction_date', startDate);
      if (endDate) query = query.lte('transaction_date', endDate);

      const { data: transactions, error } = await query;

      if (error) throw error;

      console.log('Raw cash flow transactions:', transactions);

      if (!transactions || transactions.length === 0) {
        console.log('No cash flow transactions found');
        return [];
      }

      // Group transactions by period
      const groupedData: { [key: string]: { inflow: number; outflow: number; date: string } } = {};

      transactions.forEach(transaction => {
        const date = new Date(transaction.transaction_date);
        let periodKey: string;
        let periodLabel: string;

        switch (periodType) {
          case 'day':
            periodKey = date.toISOString().split('T')[0];
            periodLabel = date.toLocaleDateString('en-US', { month: 'short', day: 'numeric' });
            break;
          case 'year':
            periodKey = date.getFullYear().toString();
            periodLabel = date.getFullYear().toString();
            break;
          default: // month
            periodKey = `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}`;
            periodLabel = date.toLocaleDateString('en-US', { month: 'short', year: 'numeric' });
        }

        if (!groupedData[periodKey]) {
          groupedData[periodKey] = { inflow: 0, outflow: 0, date: periodKey };
        }

        if (transaction.transaction_type === 'inflow') {
          groupedData[periodKey].inflow += transaction.amount;
        } else {
          groupedData[periodKey].outflow += transaction.amount;
        }
      });

      const result = Object.entries(groupedData).map(([key, data]) => ({
        period_date: data.date,
        period_label: key.includes('-') ?
          new Date(data.date + '-01').toLocaleDateString('en-US', { month: 'short', year: 'numeric' }) :
          key,
        total_inflow: data.inflow,
        total_outflow: data.outflow,
        net_flow: data.inflow - data.outflow
      }));

      console.log('Processed cash flow data:', result);
      return result;
    } catch (error) {
      console.error('Error in direct cash flow query:', error);
      return [];
    }
  }

  // Get cash flow breakdown by category
  static async getCashFlowBreakdown(startDate?: string, endDate?: string) {
    try {
      console.log('Fetching cash flow breakdown');

      let query = supabase
        .from('cash_flow_transactions')
        .select('transaction_type, category, amount');

      if (startDate) query = query.gte('transaction_date', startDate);
      if (endDate) query = query.lte('transaction_date', endDate);

      const { data, error } = await query;

      if (error) {
        console.warn('Cash flow transactions query failed, using expenses:', error);
        return await this.getCashFlowBreakdownFromExpenses(startDate, endDate);
      }

      console.log('Cash flow transactions for breakdown:', data);

      if (!data || data.length === 0) {
        console.log('No cash flow transactions, using expenses for breakdown');
        return await this.getCashFlowBreakdownFromExpenses(startDate, endDate);
      }

      // Group by category and transaction type
      const breakdown = data.reduce((acc: any, transaction) => {
        const key = `${transaction.transaction_type}_${transaction.category}`;
        if (!acc[key]) {
          acc[key] = {
            category: transaction.category,
            type: transaction.transaction_type,
            amount: 0
          };
        }
        acc[key].amount += transaction.amount;
        return acc;
      }, {});

      const result = Object.values(breakdown || {});
      console.log('Cash flow breakdown result:', result);
      return result;
    } catch (error) {
      console.error('Error getting cash flow breakdown:', error);
      return await this.getCashFlowBreakdownFromExpenses(startDate, endDate);
    }
  }

  // Fallback: Get breakdown from expenses when cash flow transactions don't exist
  static async getCashFlowBreakdownFromExpenses(startDate?: string, endDate?: string) {
    try {
      console.log('Getting breakdown from expenses');

      // Get expenses with categories
      let expenseQuery = supabase
        .from('expenses')
        .select(`
          amount,
          expense_date,
          expense_categories (
            name
          )
        `);

      if (startDate) expenseQuery = expenseQuery.gte('expense_date', startDate);
      if (endDate) expenseQuery = expenseQuery.lte('expense_date', endDate);

      const { data: expenses, error } = await expenseQuery;

      if (error) throw error;

      console.log('Expenses for breakdown:', expenses);

      // Group expenses by category
      const expenseBreakdown = expenses?.reduce((acc: any, expense: any) => {
        const categoryName = expense.expense_categories?.name || 'Other';
        const key = `outflow_${categoryName}`;

        if (!acc[key]) {
          acc[key] = {
            category: categoryName,
            type: 'outflow',
            amount: 0
          };
        }
        acc[key].amount += expense.amount;
        return acc;
      }, {});

      // Add a basic inflow category if we have revenue data
      const summary = await this.getFinancialSummary(startDate, endDate);
      if (summary.totalRevenue > 0) {
        expenseBreakdown['inflow_revenue'] = {
          category: 'Revenue',
          type: 'inflow',
          amount: summary.totalRevenue
        };
      }

      const result = Object.values(expenseBreakdown || {});
      console.log('Expense-based breakdown result:', result);
      return result;
    } catch (error) {
      console.error('Error getting breakdown from expenses:', error);
      return [];
    }
  }

  // Get revenue by month for charts
  static async getRevenueByMonth(startDate: string, endDate: string): Promise<any[]> {
    try {
      const { data, error } = await supabase
        .from('revenue_entries')
        .select('revenue_date, amount')
        .gte('revenue_date', startDate)
        .lte('revenue_date', endDate)
        .order('revenue_date');

      if (error) throw error;

      // Group by month
      const monthlyData = data.reduce((acc, entry) => {
        const month = new Date(entry.revenue_date).toLocaleDateString('en-US', { month: 'short' });
        if (!acc[month]) {
          acc[month] = { month, amount: 0 };
        }
        acc[month].amount += entry.amount || 0;
        return acc;
      }, {});

      return Object.values(monthlyData);
    } catch (error) {
      console.error('Error getting revenue by month:', error);
      return [];
    }
  }

  // Get expenses by month for charts
  static async getExpensesByMonth(startDate: string, endDate: string): Promise<any[]> {
    try {
      const { data, error } = await supabase
        .from('expenses')
        .select('expense_date, amount')
        .gte('expense_date', startDate)
        .lte('expense_date', endDate)
        .order('expense_date');

      if (error) throw error;

      // Group by month
      const monthlyData = data.reduce((acc, entry) => {
        const month = new Date(entry.expense_date).toLocaleDateString('en-US', { month: 'short' });
        if (!acc[month]) {
          acc[month] = { month, amount: 0 };
        }
        acc[month].amount += entry.amount || 0;
        return acc;
      }, {});

      return Object.values(monthlyData);
    } catch (error) {
      console.error('Error getting expenses by month:', error);
      return [];
    }
  }

  // Get cash flow by month for charts
  static async getCashFlowByMonth(startDate: string, endDate: string): Promise<any[]> {
    try {
      const [revenueData, expenseData] = await Promise.all([
        this.getRevenueByMonth(startDate, endDate),
        this.getExpensesByMonth(startDate, endDate)
      ]);

      // Combine revenue and expense data
      const months = [...new Set([
        ...revenueData.map(r => r.month),
        ...expenseData.map(e => e.month)
      ])];

      return months.map(month => {
        const revenue = revenueData.find(r => r.month === month)?.amount || 0;
        const expenses = expenseData.find(e => e.month === month)?.amount || 0;
        return {
          month,
          inflow: revenue,
          outflow: expenses,
          netFlow: revenue - expenses
        };
      });
    } catch (error) {
      console.error('Error getting cash flow by month:', error);
      return [];
    }
  }

  // Financial Analytics with Payroll Integration
  static async getFinancialSummary(startDate?: string, endDate?: string) {
    try {

      // Don't sync every time - it's causing issues
      // const syncResult = await this.syncCashFlowTransactions();
      // console.log('Cash flow sync result:', syncResult);

      // Include payroll expenses in financial calculations
      let payrollExpenses = 0;
      try {
        const { PayrollService } = await import('./payrollService');
        const payrollAnalytics = await PayrollService.getPayrollAnalytics(startDate, endDate);
        payrollExpenses = payrollAnalytics.totalGrossPay || 0;
      } catch (error) {
        console.log('Payroll data not available:', error);
      }

      // Get total revenue from invoices
      let invoiceQuery = supabase
        .from('documents')
        .select('total_amount, status')
        .eq('type', 'invoice');

      if (startDate) invoiceQuery = invoiceQuery.gte('issue_date', startDate);
      if (endDate) invoiceQuery = invoiceQuery.lte('issue_date', endDate);

      const { data: invoices } = await invoiceQuery;

      const invoiceRevenue = invoices?.reduce((sum, inv) => sum + (inv.total_amount || 0), 0) || 0;
      // Handle both 'paid' and 'Paid' status (case insensitive)
      const paidInvoiceRevenue = invoices?.filter(inv =>
        inv.status?.toLowerCase() === 'paid'
      ).reduce((sum, inv) => sum + (inv.total_amount || 0), 0) || 0;

      // Get total revenue from revenue entries
      let revenueQuery = supabase
        .from('revenue_entries')
        .select('amount, status');

      if (startDate) revenueQuery = revenueQuery.gte('revenue_date', startDate);
      if (endDate) revenueQuery = revenueQuery.lte('revenue_date', endDate);

      const { data: revenueEntries } = await revenueQuery;
      const revenueEntriesTotal = revenueEntries?.reduce((sum, entry) => sum + (entry.amount || 0), 0) || 0;
      // Count entries as received if status is 'received' OR if status is null/undefined (legacy data)
      const receivedRevenueEntries = revenueEntries?.filter(entry =>
        entry.status === 'received' || entry.status === null || entry.status === undefined
      ).reduce((sum, entry) => sum + (entry.amount || 0), 0) || 0;

      // Combine both sources of revenue
      const totalRevenue = invoiceRevenue + revenueEntriesTotal;
      const paidRevenue = paidInvoiceRevenue + receivedRevenueEntries;

      console.log('💰 Revenue calculation:', {
        invoiceRevenue,
        revenueEntriesTotal,
        totalRevenue,
        paidInvoiceRevenue,
        receivedRevenueEntries,
        paidRevenue
      });

      // Get total expenses
      let expenseQuery = supabase
        .from('expenses')
        .select('amount, status');

      if (startDate) expenseQuery = expenseQuery.gte('expense_date', startDate);
      if (endDate) expenseQuery = expenseQuery.lte('expense_date', endDate);

      const { data: expenses } = await expenseQuery;
      const regularExpenses = expenses?.reduce((sum, exp) => sum + (exp.amount || 0), 0) || 0;
      const paidExpenses = expenses?.filter(exp => exp.status === 'paid').reduce((sum, exp) => sum + (exp.amount || 0), 0) || 0;

      // Include payroll expenses in total expenses
      const totalExpenses = regularExpenses + payrollExpenses;
      console.log('💰 Total expenses calculated:', { regularExpenses, payrollExpenses, totalExpenses });

      // Get actual cash flow from transactions with detailed logging
      console.log('💰 Querying cash flow transactions...');

      // First, get ALL transactions to see what we have
      const { data: allCashFlow, error: allCashFlowError } = await supabase
        .from('cash_flow_transactions')
        .select('transaction_type, amount, transaction_date, category, description');

      console.log('📊 All cash flow transactions:', {
        count: allCashFlow?.length,
        error: allCashFlowError,
        sample: allCashFlow?.slice(0, 3)
      });

      // Now apply date filters if provided
      let cashFlow = allCashFlow;
      if (startDate || endDate) {
        console.log('🔍 Applying date filters...');
        cashFlow = allCashFlow?.filter(transaction => {
          const transactionDate = new Date(transaction.transaction_date);
          const start = startDate ? new Date(startDate) : new Date('1900-01-01');
          const end = endDate ? new Date(endDate) : new Date('2100-12-31');
          return transactionDate >= start && transactionDate <= end;
        });
        console.log('📅 Filtered transactions:', {
          originalCount: allCashFlow?.length,
          filteredCount: cashFlow?.length,
          startDate,
          endDate
        });
      }

      const cashFlowError = allCashFlowError;

      console.log('Cash flow query result:', {
        cashFlow,
        cashFlowError,
        count: cashFlow?.length,
        startDate,
        endDate
      });

      if (cashFlowError) {
        console.error('Cash flow query error:', cashFlowError);
        throw cashFlowError;
      }

      // Calculate totals with detailed logging
      const inflowTransactions = cashFlow?.filter(t => t.transaction_type === 'inflow') || [];
      const outflowTransactions = cashFlow?.filter(t => t.transaction_type === 'outflow') || [];

      console.log('Inflow transactions:', inflowTransactions);
      console.log('Outflow transactions:', outflowTransactions);

      const totalInflow = inflowTransactions.reduce((sum, t) => {
        const amount = Number(t.amount) || 0;
        console.log(`Adding inflow: ${amount} from ${t.description}`);
        return sum + amount;
      }, 0);

      const totalOutflow = outflowTransactions.reduce((sum, t) => {
        const amount = Number(t.amount) || 0;
        console.log(`Adding outflow: ${amount} from ${t.description}`);
        return sum + amount;
      }, 0);

      console.log('🎯 Final calculated cash flow:', {
        totalInflow,
        totalOutflow,
        netCashFlow: totalInflow - totalOutflow,
        inflowCount: inflowTransactions.length,
        outflowCount: outflowTransactions.length,
        paidRevenue,
        paidExpenses
      });

      // Use revenue entries as primary revenue source, cash flow for cash tracking
      const finalResult = {
        totalRevenue: revenueEntriesTotal, // Use actual revenue entries total
        totalExpenses: totalOutflow, // Use cash flow outflow as expenses
        netProfit: revenueEntriesTotal - totalOutflow,
        totalInflow: totalInflow, // Keep cash flow for cash tracking
        totalOutflow: totalOutflow, // Keep cash flow for cash tracking
        netCashFlow: totalInflow - totalOutflow,
        paidRevenue: receivedRevenueEntries, // Only received revenue entries
        paidExpenses: totalOutflow,
        outstandingRevenue: revenueEntriesTotal - receivedRevenueEntries, // Pending revenue
        pendingExpenses: 0
      };

      console.log('🚀 Returning financial summary:', finalResult);
      return finalResult;
    } catch (error) {
      console.error('Error getting financial summary:', error);
      return {
        totalRevenue: 0,
        totalExpenses: 0,
        netProfit: 0,
        totalInflow: 0,
        totalOutflow: 0,
        netCashFlow: 0,
        paidRevenue: 0,
        paidExpenses: 0,
        outstandingRevenue: 0,
        pendingExpenses: 0
      };
    }
  }

  // Revenue Management - Full Database Integration
  static async createRevenueEntry(revenueData: {
    client_name: string;
    project_name?: string;
    amount: number;
    revenue_date: string;
    payment_method?: string;
    category: string;
    description: string;
    status: 'received' | 'pending' | 'invoiced';
    invoice_number?: string;
    reference_number?: string;
  }) {
    try {
      console.log('🔄 Creating revenue entry in database:', revenueData);

      // 1. Create revenue entry in database
      const { data: revenueEntry, error: revenueError } = await supabase
        .from('revenue_entries')
        .insert([{
          client_name: revenueData.client_name,
          project_name: revenueData.project_name,
          amount: revenueData.amount,
          revenue_date: revenueData.revenue_date,
          payment_method: revenueData.payment_method,
          category: revenueData.category,
          description: revenueData.description,
          status: revenueData.status,
          invoice_number: revenueData.invoice_number,
          reference_number: revenueData.reference_number
        }])
        .select()
        .single();

      if (revenueError) {
        console.error('❌ Revenue entry error:', revenueError);
        throw revenueError;
      }

      console.log('✅ Revenue entry saved to database:', revenueEntry);

      // 2. If revenue is received, create cash flow transaction
      if (revenueData.status === 'received') {
        console.log('💰 Revenue is received, creating cash flow transaction...');

        const { data: cashFlowData, error: cashFlowError } = await supabase
          .from('cash_flow_transactions')
          .insert([{
            transaction_type: 'inflow',
            category: revenueData.category,
            description: `${revenueData.category}: ${revenueData.description} (${revenueData.client_name})`,
            amount: revenueData.amount,
            transaction_date: revenueData.revenue_date,
            reference_id: revenueEntry.id,
            reference_type: 'revenue',
            account_type: revenueData.payment_method?.toLowerCase().replace(' ', '_') || 'bank'
          }])
          .select()
          .single();

        if (cashFlowError) {
          console.error('❌ Cash flow transaction error:', cashFlowError);
          throw cashFlowError;
        }

        console.log('✅ Cash flow transaction created:', cashFlowData);

        // 3. Also create payment record
        const { data: paymentData, error: paymentError } = await supabase
          .from('payment_records')
          .insert([{
            payment_amount: revenueData.amount,
            payment_date: revenueData.revenue_date,
            payment_method: revenueData.payment_method || 'Bank Transfer',
            reference_number: revenueData.reference_number,
            notes: `${revenueData.category}: ${revenueData.description} (Client: ${revenueData.client_name})`,
            status: 'completed'
          }])
          .select()
          .single();

        if (paymentError) {
          console.warn('⚠️ Payment record error (continuing):', paymentError);
        } else {
          console.log('✅ Payment record created:', paymentData);
        }
      }

      console.log('🎉 Revenue entry process completed successfully');
      return revenueEntry;
    } catch (error) {
      console.error('❌ Error creating revenue entry:', error);
      throw error;
    }
  }

  // Get Revenue Entries
  static async getRevenueEntries() {
    try {
      const { data, error } = await supabase
        .from('revenue_entries')
        .select('*')
        .order('revenue_date', { ascending: false });

      if (error) throw error;
      return data || [];
    } catch (error) {
      console.error('Error getting revenue entries:', error);
      return [];
    }
  }

  // Get Recent Activity from Real Database Transactions
  static async getRecentActivity(limit: number = 10) {
    try {
      console.log('🔄 Loading recent activity from database...');
      const activity = [];

      // Get recent revenue entries
      const { data: revenueEntries } = await supabase
        .from('revenue_entries')
        .select('*')
        .order('revenue_date', { ascending: false })
        .limit(20);

      if (revenueEntries) {
        revenueEntries.forEach(entry => {
          activity.push({
            type: 'revenue',
            date: entry.revenue_date,
            client: entry.client_name,
            amount: entry.amount,
            description: `${entry.category} - ${entry.description}`,
            status: entry.status,
            id: entry.id,
            project: entry.project_name,
            payment_method: entry.payment_method
          });
        });
      }

      // Get recent expenses
      const { data: expenses } = await supabase
        .from('expenses')
        .select('*')
        .order('expense_date', { ascending: false })
        .limit(20);

      if (expenses) {
        expenses.forEach(expense => {
          activity.push({
            type: 'expense',
            date: expense.expense_date,
            client: expense.vendor || 'Internal',
            amount: -expense.amount, // Negative for expenses
            description: `${expense.category} - ${expense.description}`,
            status: expense.status,
            id: expense.id,
            project: expense.project_name
          });
        });
      }

      // Get recent cash flow transactions
      const { data: cashFlowTransactions } = await supabase
        .from('cash_flow_transactions')
        .select('*')
        .order('transaction_date', { ascending: false })
        .limit(20);

      if (cashFlowTransactions) {
        cashFlowTransactions.forEach(transaction => {
          activity.push({
            type: transaction.transaction_type,
            date: transaction.transaction_date,
            client: transaction.description.includes('Client') ? 'Client Transaction' : 'Internal',
            amount: transaction.transaction_type === 'inflow' ? transaction.amount : -transaction.amount,
            description: transaction.description,
            status: 'completed',
            id: transaction.id,
            category: transaction.category
          });
        });
      }

      // Sort by date (most recent first) and limit
      const sortedActivity = activity
        .sort((a, b) => new Date(b.date).getTime() - new Date(a.date).getTime())
        .slice(0, limit);

      console.log('✅ Loaded recent activity:', sortedActivity);
      return sortedActivity;
    } catch (error) {
      console.error('❌ Error getting recent activity:', error);
      return [];
    }
  }

  // Automatically create revenue entry when invoice is marked as paid
  static async handleInvoicePayment(invoiceData: {
    invoice_id: string;
    invoice_number: string;
    client_name: string;
    project_name?: string;
    total_amount: number;
    payment_date?: string;
    payment_method?: string;
    reference_number?: string;
  }) {
    try {
      console.log('💰 Processing invoice payment for revenue:', invoiceData);

      // Check if revenue entry already exists for this invoice
      const { data: existingRevenue } = await supabase
        .from('revenue_entries')
        .select('id')
        .eq('invoice_number', invoiceData.invoice_number)
        .single();

      if (existingRevenue) {
        console.log('⚠️ Revenue entry already exists for invoice:', invoiceData.invoice_number);
        return existingRevenue;
      }

      // Create revenue entry for the paid invoice
      const revenueData = {
        client_name: invoiceData.client_name,
        project_name: invoiceData.project_name || '',
        amount: invoiceData.total_amount,
        revenue_date: invoiceData.payment_date || new Date().toISOString().split('T')[0],
        payment_method: invoiceData.payment_method || 'Invoice Payment',
        category: 'Invoice Payment',
        description: `Payment received for invoice ${invoiceData.invoice_number}`,
        status: 'received' as const,
        invoice_number: invoiceData.invoice_number,
        reference_number: invoiceData.reference_number || `INV-${invoiceData.invoice_number}`
      };

      console.log('🔄 Creating revenue entry for paid invoice:', revenueData);
      const revenueEntry = await this.createRevenueEntry(revenueData);

      console.log('✅ Revenue entry created for paid invoice:', revenueEntry);
      return revenueEntry;
    } catch (error) {
      console.error('❌ Error handling invoice payment:', error);
      throw error;
    }
  }

  // Enhanced invoice status update with automatic revenue recognition
  static async updateInvoiceStatus(invoiceId: string, newStatus: string, paymentDetails?: {
    payment_date?: string;
    payment_method?: string;
    reference_number?: string;
  }, updated_by?: string) {
    try {
      console.log('📄 Updating invoice status:', { invoiceId, newStatus, paymentDetails });

      // First, get the invoice details
      const { data: invoice, error: fetchError } = await supabase
        .from('documents')
        .select('*')
        .eq('id', invoiceId)
        .eq('type', 'invoice')
        .single();

      if (fetchError || !invoice) {
        console.error('❌ Error fetching invoice:', fetchError);
        throw new Error('Invoice not found');
      }

      // Validate status change
      if (invoice.status === newStatus) {
        console.log('⚠️ Invoice status unchanged:', newStatus);
        return { invoice, revenue: null };
      }

      // Update the invoice status
      const { data: updatedInvoice, error: updateError } = await supabase
        .from('documents')
        .update({
          status: newStatus,
          last_modified: new Date().toISOString()
        })
        .eq('id', invoiceId)
        .select()
        .single();

      if (updateError) {
        console.error('❌ Error updating invoice status:', updateError);
        throw updateError;
      }

      console.log('✅ Invoice status updated:', updatedInvoice);

      // If status is 'paid' (case insensitive), automatically create revenue entry
      if (newStatus.toLowerCase() === 'paid') {
        console.log('💰 Invoice marked as paid, creating revenue entry...');

        const revenueEntry = await this.handleInvoicePayment({
          invoice_id: invoice.id,
          invoice_number: invoice.document_number,
          client_name: invoice.client_name,
          project_name: invoice.project_name,
          total_amount: invoice.total_amount,
          payment_date: paymentDetails?.payment_date,
          payment_method: paymentDetails?.payment_method,
          reference_number: paymentDetails?.reference_number
        });

        // Send payment received notification
        if (updated_by) {
          try {
            const { NotificationIntegration } = await import('./notificationIntegration');
            await NotificationIntegration.handlePaymentReceived({
              amount: invoice.total_amount,
              payment_date: paymentDetails?.payment_date || new Date().toISOString(),
              payment_method: paymentDetails?.payment_method || 'Unknown',
              reference_number: paymentDetails?.reference_number
            }, updatedInvoice);
          } catch (notificationError) {
            console.error('Error sending payment notification:', notificationError);
            // Don't fail the payment processing if notification fails
          }
        }

        console.log('🎉 Invoice payment processed and revenue created!');
        return { invoice: updatedInvoice, revenue: revenueEntry };
      }

      return { invoice: updatedInvoice, revenue: null };
    } catch (error) {
      console.error('❌ Error updating invoice status:', error);
      throw error;
    }
  }

  // Sync existing paid invoices to revenue entries (one-time migration)
  static async syncPaidInvoicesToRevenue() {
    try {
      console.log('🔄 Syncing existing paid invoices to revenue entries...');

      // Get all paid invoices from documents table (case insensitive)
      const { data: allInvoices, error: invoicesError } = await supabase
        .from('documents')
        .select('*')
        .eq('type', 'invoice');

      if (invoicesError) {
        console.error('❌ Error fetching invoices:', invoicesError);
        throw invoicesError;
      }

      // Filter for paid invoices (case insensitive)
      const paidInvoices = allInvoices?.filter(inv =>
        inv.status?.toLowerCase() === 'paid'
      ) || [];



      if (!paidInvoices || paidInvoices.length === 0) {
        console.log('ℹ️ No paid invoices found to sync');
        return { synced: 0, skipped: 0, errors: 0 };
      }

      let synced = 0;
      let skipped = 0;
      let errors = 0;

      for (const invoice of paidInvoices) {
        try {
          // Check if revenue entry already exists
          const { data: existingRevenue } = await supabase
            .from('revenue_entries')
            .select('id')
            .eq('invoice_number', invoice.document_number)
            .single();

          if (existingRevenue) {
            console.log(`⏭️ Revenue entry already exists for invoice ${invoice.document_number}`);
            skipped++;
            continue;
          }

          // Create revenue entry for this paid invoice
          await this.handleInvoicePayment({
            invoice_id: invoice.id,
            invoice_number: invoice.document_number,
            client_name: invoice.client_name,
            project_name: invoice.project_name,
            total_amount: invoice.total_amount,
            payment_date: invoice.last_modified || new Date().toISOString().split('T')[0],
            payment_method: 'Invoice Payment',
            reference_number: `SYNC-${invoice.document_number}`
          });

          console.log(`✅ Synced invoice ${invoice.document_number} to revenue`);
          synced++;
        } catch (error) {
          console.error(`❌ Error syncing invoice ${invoice.document_number}:`, error);
          errors++;
        }
      }

      const result = { synced, skipped, errors, total: paidInvoices.length };
      console.log('🎉 Sync completed:', result);
      return result;
    } catch (error) {
      console.error('❌ Error syncing paid invoices to revenue:', error);
      throw error;
    }
  }

  // Sync existing revenue entries and expenses to cash flow transactions
  static async syncToCashFlowTransactions() {
    try {
      console.log('🔄 Syncing revenue and expenses to cash flow transactions...');

      let synced = 0;
      let skipped = 0;
      let errors = 0;

      // 1. Sync revenue entries to cash flow inflows
      const { data: revenueEntries } = await supabase
        .from('revenue_entries')
        .select('*')
        .eq('status', 'received');

      if (revenueEntries) {
        for (const revenue of revenueEntries) {
          try {
            // Check if cash flow transaction already exists
            const { data: existingCashFlow } = await supabase
              .from('cash_flow_transactions')
              .select('id')
              .eq('reference_id', revenue.id)
              .eq('reference_type', 'revenue')
              .single();

            if (existingCashFlow) {
              skipped++;
              continue;
            }

            // Create cash flow transaction for revenue
            await supabase
              .from('cash_flow_transactions')
              .insert([{
                transaction_type: 'inflow',
                category: revenue.category,
                description: `${revenue.category}: ${revenue.description} (${revenue.client_name})`,
                amount: revenue.amount,
                transaction_date: revenue.revenue_date,
                reference_id: revenue.id,
                reference_type: 'revenue',
                account_type: revenue.payment_method?.toLowerCase().replace(' ', '_') || 'bank'
              }]);

            synced++;
            console.log(`✅ Synced revenue ${revenue.id} to cash flow`);
          } catch (error) {
            console.error(`❌ Error syncing revenue ${revenue.id}:`, error);
            errors++;
          }
        }
      }

      // 2. Sync expenses to cash flow outflows
      const { data: expenses } = await supabase
        .from('expenses')
        .select('*')
        .eq('status', 'paid');

      if (expenses) {
        for (const expense of expenses) {
          try {
            // Check if cash flow transaction already exists
            const { data: existingCashFlow } = await supabase
              .from('cash_flow_transactions')
              .select('id')
              .eq('reference_id', expense.id)
              .eq('reference_type', 'expense')
              .single();

            if (existingCashFlow) {
              skipped++;
              continue;
            }

            // Create cash flow transaction for expense
            await supabase
              .from('cash_flow_transactions')
              .insert([{
                transaction_type: 'outflow',
                category: expense.category || 'Other',
                description: expense.description,
                amount: expense.amount,
                transaction_date: expense.expense_date,
                reference_id: expense.id,
                reference_type: 'expense',
                account_type: expense.payment_method?.toLowerCase().replace(' ', '_') || 'cash'
              }]);

            synced++;
            console.log(`✅ Synced expense ${expense.id} to cash flow`);
          } catch (error) {
            console.error(`❌ Error syncing expense ${expense.id}:`, error);
            errors++;
          }
        }
      }

      const result = { synced, skipped, errors };
      console.log('🎉 Cash flow sync completed:', result);
      return result;
    } catch (error) {
      console.error('❌ Error syncing to cash flow transactions:', error);
      throw error;
    }
  }

  // Update Revenue Entry
  static async updateRevenueEntry(id: string, updates: Partial<{
    client_name: string;
    project_name: string;
    amount: number;
    revenue_date: string;
    payment_method: string;
    category: string;
    description: string;
    status: 'received' | 'pending' | 'invoiced';
    invoice_number: string;
    reference_number: string;
  }>) {
    try {
      const { data, error } = await supabase
        .from('revenue_entries')
        .update(updates)
        .eq('id', id)
        .select()
        .single();

      if (error) throw error;
      return data;
    } catch (error) {
      console.error('Error updating revenue entry:', error);
      throw error;
    }
  }

  // Delete Revenue Entry
  static async deleteRevenueEntry(id: string) {
    try {
      const { error } = await supabase
        .from('revenue_entries')
        .delete()
        .eq('id', id);

      if (error) throw error;
      return true;
    } catch (error) {
      console.error('Error deleting revenue entry:', error);
      throw error;
    }
  }

  // Invoice Analytics
  static async getInvoiceAnalytics() {
    try {
      // This would integrate with your documents system
      // For now, return mock data structure
      return {
        totalInvoices: 0,
        paidInvoices: 0,
        pendingInvoices: 0,
        overdueInvoices: 0,
        totalInvoiceValue: 0,
        paidValue: 0,
        outstandingValue: 0
      };
    } catch (error) {
      console.error('Error getting invoice analytics:', error);
      return {
        totalInvoices: 0,
        paidInvoices: 0,
        pendingInvoices: 0,
        overdueInvoices: 0,
        totalInvoiceValue: 0,
        paidValue: 0,
        outstandingValue: 0
      };
    }
  }

  // Payroll Integration - Create expense entries from payroll data
  static async createPayrollExpenseEntries(payrollPeriodId: string): Promise<boolean> {
    try {
      console.log('🔄 Creating payroll expense entries for period:', payrollPeriodId);

      // Get payroll category (create if doesn't exist)
      let payrollCategory = await this.getExpenseCategoryByName('Payroll');
      if (!payrollCategory) {
        // Create payroll category if it doesn't exist
        const { data: newCategory, error: categoryError } = await supabase
          .from('expense_categories')
          .insert([{
            name: 'Payroll',
            description: 'Employee salaries and wages',
            is_project_related: false
          }])
          .select()
          .single();

        if (categoryError) {
          console.error('Error creating payroll category:', categoryError);
          throw categoryError;
        }
        payrollCategory = newCategory;
      }

      // Get payroll period data
      const { data: payrollPeriod, error: periodError } = await supabase
        .from('payroll_periods')
        .select(`
          *,
          payroll_entries:payroll_entries(
            *,
            user_profile:user_profiles(first_name, last_name)
          )
        `)
        .eq('id', payrollPeriodId)
        .single();

      if (periodError || !payrollPeriod) {
        throw new Error('Payroll period not found');
      }

      // Check if expense already exists for this payroll period
      const { data: existingExpense } = await supabase
        .from('expenses')
        .select('id')
        .eq('metadata->payroll_period_id', payrollPeriodId)
        .single();

      if (existingExpense) {
        console.log('⚠️ Payroll expense already exists for this period');
        return true;
      }

      // Create main payroll expense entry
      const payrollExpense = {
        category_id: payrollCategory.id,
        description: `Payroll for period ${new Date(payrollPeriod.period_start).toLocaleDateString()} - ${new Date(payrollPeriod.period_end).toLocaleDateString()}`,
        amount: payrollPeriod.total_gross_pay || 0,
        expense_date: payrollPeriod.pay_date,
        payment_method: 'Bank Transfer',
        vendor_name: 'Company Payroll',
        status: 'paid' as const,
        metadata: {
          payroll_period_id: payrollPeriodId,
          type: 'payroll',
          employee_count: payrollPeriod.payroll_entries?.length || 0
        }
      };

      const createdExpense = await this.createExpense(payrollExpense);

      if (createdExpense) {
        console.log('✅ Payroll expense created:', createdExpense);

        // Create individual expense entries for overtime if significant
        const overtimeEntries = payrollPeriod.payroll_entries?.filter((entry: any) =>
          (entry.overtime_hours || 0) > 0
        ) || [];

        if (overtimeEntries.length > 0) {
          const totalOvertimePay = overtimeEntries.reduce((sum: number, entry: any) =>
            sum + ((entry.overtime_hours || 0) * (entry.overtime_rate || 0)), 0
          );

          if (totalOvertimePay > 0) {
            const overtimeExpense = {
              category_id: payrollCategory.id,
              description: `Overtime pay for period ${new Date(payrollPeriod.period_start).toLocaleDateString()} - ${new Date(payrollPeriod.period_end).toLocaleDateString()}`,
              amount: totalOvertimePay,
              expense_date: payrollPeriod.pay_date,
              payment_method: 'Bank Transfer',
              vendor_name: 'Company Payroll - Overtime',
              status: 'paid' as const,
              metadata: {
                payroll_period_id: payrollPeriodId,
                type: 'overtime',
                employee_count: overtimeEntries.length
              }
            };

            await this.createExpense(overtimeExpense);
            console.log('✅ Overtime expense created');
          }
        }

        return true;
      }

      return false;
    } catch (error) {
      console.error('❌ Error creating payroll expense entries:', error);
      return false;
    }
  }

  // Helper function to get expense category by name
  static async getExpenseCategoryByName(name: string): Promise<ExpenseCategory | null> {
    try {
      const { data, error } = await supabase
        .from('expense_categories')
        .select('*')
        .eq('name', name)
        .single();

      if (error) return null;
      return data;
    } catch (error) {
      return null;
    }
  }

  // Sync all payroll periods to expenses
  static async syncAllPayrollExpenses(): Promise<{ processed: number; errors: number }> {
    try {
      console.log('🔄 Syncing all payroll periods to expenses...');

      const { data: payrollPeriods, error } = await supabase
        .from('payroll_periods')
        .select('id, period_start, period_end, status')
        .eq('status', 'completed')
        .order('period_start', { ascending: false });

      if (error) throw error;

      let processed = 0;
      let errors = 0;

      for (const period of payrollPeriods || []) {
        try {
          const success = await this.createPayrollExpenseEntries(period.id);
          if (success) {
            processed++;
          } else {
            errors++;
          }
        } catch (error) {
          console.error(`Error processing payroll period ${period.id}:`, error);
          errors++;
        }
      }

      console.log(`✅ Payroll sync complete: ${processed} processed, ${errors} errors`);
      return { processed, errors };
    } catch (error) {
      console.error('❌ Error syncing payroll expenses:', error);
      return { processed: 0, errors: 1 };
    }
  }
}
