import { supabase } from './supabase'
import { PersonalExpenseService, PersonalExpense, PersonalBudget, ExpenseCategory } from './personalExpenseService'

export interface ExpenseReport {
  period: string
  totalExpenses: number
  totalBudget: number
  budgetUtilization: number
  categoryBreakdown: CategoryBreakdown[]
  budgetBreakdown: BudgetBreakdown[]
  monthlyTrend: MonthlyTrend[]
  topExpenses: PersonalExpense[]
  summary: ReportSummary
}

export interface CategoryBreakdown {
  category: ExpenseCategory
  totalAmount: number
  expenseCount: number
  percentage: number
  budgetAllocated: number
  budgetUsed: number
  budgetRemaining: number
  isOverBudget: boolean
}

export interface BudgetBreakdown {
  budget: PersonalBudget
  totalSpent: number
  remainingAmount: number
  utilizationPercentage: number
  daysRemaining: number
  dailyBudgetRemaining: number
  isOverBudget: boolean
  expenses: PersonalExpense[]
}

export interface MonthlyTrend {
  month: string
  totalExpenses: number
  totalBudget: number
  utilizationPercentage: number
  expenseCount: number
}

export interface ReportSummary {
  totalCategories: number
  totalBudgets: number
  averageExpenseAmount: number
  mostExpensiveCategory: string
  leastExpensiveCategory: string
  budgetComplianceRate: number
  savingsRate: number
  recommendations: string[]
}

export class PersonalExpenseReportService {
  // Generate comprehensive expense report
  static async generateExpenseReport(
    startDate: string,
    endDate: string,
    userId?: string
  ): Promise<ExpenseReport> {
    try {
      console.log('Generating expense report for period:', startDate, 'to', endDate)

      // Get all expenses for the period
      const expenses = await PersonalExpenseService.getExpensesByDateRange(startDate, endDate)
      
      // Get all budgets
      const budgets = await PersonalExpenseService.getPersonalBudgets()
      
      // Get all categories
      const categories = await PersonalExpenseService.getExpenseCategories()

      // Calculate totals
      const totalExpenses = expenses.reduce((sum, expense) => sum + expense.amount, 0)
      const totalBudget = budgets.reduce((sum, budget) => sum + budget.amount, 0)
      const budgetUtilization = totalBudget > 0 ? (totalExpenses / totalBudget) * 100 : 0

      // Generate category breakdown
      const categoryBreakdown = await this.generateCategoryBreakdown(expenses, categories, budgets)
      
      // Generate budget breakdown
      const budgetBreakdown = await this.generateBudgetBreakdown(expenses, budgets)
      
      // Generate monthly trend
      const monthlyTrend = await this.generateMonthlyTrend(startDate, endDate)
      
      // Get top expenses
      const topExpenses = expenses
        .sort((a, b) => b.amount - a.amount)
        .slice(0, 10)

      // Generate summary
      const summary = this.generateReportSummary(expenses, categories, budgets, categoryBreakdown)

      return {
        period: `${startDate} to ${endDate}`,
        totalExpenses,
        totalBudget,
        budgetUtilization,
        categoryBreakdown,
        budgetBreakdown,
        monthlyTrend,
        topExpenses,
        summary
      }
    } catch (error) {
      console.error('Error generating expense report:', error)
      throw error
    }
  }

  // Generate category breakdown analysis
  private static async generateCategoryBreakdown(
    expenses: PersonalExpense[],
    categories: ExpenseCategory[],
    budgets: PersonalBudget[]
  ): Promise<CategoryBreakdown[]> {
    const categoryMap = new Map<string, CategoryBreakdown>()

    // Initialize categories
    categories.forEach(category => {
      categoryMap.set(category.id, {
        category,
        totalAmount: 0,
        expenseCount: 0,
        percentage: 0,
        budgetAllocated: 0,
        budgetUsed: 0,
        budgetRemaining: 0,
        isOverBudget: false
      })
    })

    // Calculate expenses by category
    expenses.forEach(expense => {
      if (expense.category_id && categoryMap.has(expense.category_id)) {
        const breakdown = categoryMap.get(expense.category_id)!
        breakdown.totalAmount += expense.amount
        breakdown.expenseCount += 1
      }
    })

    // Calculate budget allocations by category
    budgets.forEach(budget => {
      if (budget.category_id && categoryMap.has(budget.category_id)) {
        const breakdown = categoryMap.get(budget.category_id)!
        breakdown.budgetAllocated += budget.amount
      }
    })

    // Calculate percentages and remaining amounts
    const totalExpenses = expenses.reduce((sum, expense) => sum + expense.amount, 0)
    
    categoryMap.forEach(breakdown => {
      breakdown.percentage = totalExpenses > 0 ? (breakdown.totalAmount / totalExpenses) * 100 : 0
      breakdown.budgetUsed = breakdown.totalAmount
      breakdown.budgetRemaining = breakdown.budgetAllocated - breakdown.budgetUsed
      breakdown.isOverBudget = breakdown.budgetUsed > breakdown.budgetAllocated
    })

    return Array.from(categoryMap.values())
      .filter(breakdown => breakdown.totalAmount > 0 || breakdown.budgetAllocated > 0)
      .sort((a, b) => b.totalAmount - a.totalAmount)
  }

  // Generate budget breakdown analysis
  private static async generateBudgetBreakdown(
    expenses: PersonalExpense[],
    budgets: PersonalBudget[]
  ): Promise<BudgetBreakdown[]> {
    console.log('Generating budget breakdown with budgets:', budgets)

    return budgets.map(budget => {
      console.log('Processing budget:', budget)

      // Get expenses for this budget
      const budgetExpenses = expenses.filter(expense => expense.budget_id === budget.id)
      const totalSpent = budgetExpenses.reduce((sum, expense) => sum + expense.amount, 0)
      const remainingAmount = (budget.amount || 0) - totalSpent
      const utilizationPercentage = (budget.amount || 0) > 0 ? (totalSpent / (budget.amount || 0)) * 100 : 0

      // Calculate days remaining in budget period
      const endDate = new Date(budget.end_date || new Date())
      const today = new Date()
      const daysRemaining = Math.max(0, Math.ceil((endDate.getTime() - today.getTime()) / (1000 * 60 * 60 * 24)))
      const dailyBudgetRemaining = daysRemaining > 0 ? remainingAmount / daysRemaining : 0

      const breakdown = {
        budget,
        totalSpent,
        remainingAmount,
        utilizationPercentage,
        daysRemaining,
        dailyBudgetRemaining,
        isOverBudget: totalSpent > (budget.amount || 0),
        expenses: budgetExpenses.sort((a, b) => b.amount - a.amount)
      }

      console.log('Generated budget breakdown:', breakdown)
      return breakdown
    }).sort((a, b) => b.utilizationPercentage - a.utilizationPercentage)
  }

  // Generate monthly trend analysis
  private static async generateMonthlyTrend(startDate: string, endDate: string): Promise<MonthlyTrend[]> {
    try {
      const start = new Date(startDate)
      const end = new Date(endDate)
      const trends: MonthlyTrend[] = []

      // Generate monthly data for the past 12 months or the specified range
      const monthsToAnalyze = Math.min(12, Math.ceil((end.getTime() - start.getTime()) / (1000 * 60 * 60 * 24 * 30)))
      
      for (let i = monthsToAnalyze - 1; i >= 0; i--) {
        const monthStart = new Date(end.getFullYear(), end.getMonth() - i, 1)
        const monthEnd = new Date(end.getFullYear(), end.getMonth() - i + 1, 0)
        
        const monthStartStr = monthStart.toISOString().split('T')[0]
        const monthEndStr = monthEnd.toISOString().split('T')[0]

        // Get expenses for this month
        const monthExpenses = await PersonalExpenseService.getExpensesByDateRange(monthStartStr, monthEndStr)
        const totalExpenses = monthExpenses.reduce((sum, expense) => sum + expense.amount, 0)

        // Get budgets active during this month
        const budgets = await PersonalExpenseService.getPersonalBudgets()
        const activeBudgets = budgets.filter(budget => {
          const budgetStart = new Date(budget.start_date)
          const budgetEnd = new Date(budget.end_date)
          return budgetStart <= monthEnd && budgetEnd >= monthStart
        })
        const totalBudget = activeBudgets.reduce((sum, budget) => sum + budget.amount, 0)

        trends.push({
          month: monthStart.toLocaleDateString('en-US', { year: 'numeric', month: 'short' }),
          totalExpenses,
          totalBudget,
          utilizationPercentage: totalBudget > 0 ? (totalExpenses / totalBudget) * 100 : 0,
          expenseCount: monthExpenses.length
        })
      }

      return trends
    } catch (error) {
      console.error('Error generating monthly trend:', error)
      return []
    }
  }

  // Generate report summary with insights
  private static generateReportSummary(
    expenses: PersonalExpense[],
    categories: ExpenseCategory[],
    budgets: PersonalBudget[],
    categoryBreakdown: CategoryBreakdown[]
  ): ReportSummary {
    const totalExpenses = expenses.reduce((sum, expense) => sum + expense.amount, 0)
    const totalBudget = budgets.reduce((sum, budget) => sum + budget.amount, 0)
    const averageExpenseAmount = expenses.length > 0 ? totalExpenses / expenses.length : 0

    // Find most and least expensive categories
    const sortedCategories = categoryBreakdown.sort((a, b) => b.totalAmount - a.totalAmount)
    const mostExpensiveCategory = sortedCategories[0]?.category.name || 'N/A'
    const leastExpensiveCategory = sortedCategories[sortedCategories.length - 1]?.category.name || 'N/A'

    // Calculate budget compliance rate
    const budgetsInCompliance = budgets.filter(budget => {
      const spent = expenses
        .filter(expense => expense.budget_id === budget.id)
        .reduce((sum, expense) => sum + expense.amount, 0)
      return spent <= budget.amount
    }).length
    const budgetComplianceRate = budgets.length > 0 ? (budgetsInCompliance / budgets.length) * 100 : 100

    // Calculate savings rate
    const savingsRate = totalBudget > 0 ? ((totalBudget - totalExpenses) / totalBudget) * 100 : 0

    // Generate recommendations
    const recommendations: string[] = []
    
    if (savingsRate < 10) {
      recommendations.push('Consider reducing expenses to improve your savings rate')
    }
    if (budgetComplianceRate < 80) {
      recommendations.push('Review and adjust your budgets to better match your spending patterns')
    }
    if (categoryBreakdown.some(cat => cat.isOverBudget)) {
      recommendations.push('Some categories are over budget - consider reallocating funds or reducing spending')
    }
    if (averageExpenseAmount > 100) {
      recommendations.push('Look for opportunities to reduce large expenses')
    }
    if (recommendations.length === 0) {
      recommendations.push('Great job managing your expenses and staying within budget!')
    }

    return {
      totalCategories: categories.length,
      totalBudgets: budgets.length,
      averageExpenseAmount,
      mostExpensiveCategory,
      leastExpensiveCategory,
      budgetComplianceRate,
      savingsRate,
      recommendations
    }
  }

  // Get quick expense statistics
  static async getExpenseStatistics(userId?: string): Promise<{
    thisMonth: number
    lastMonth: number
    thisYear: number
    averageMonthly: number
    topCategory: string
    budgetUtilization: number
  }> {
    try {
      const now = new Date()
      const thisMonthStart = new Date(now.getFullYear(), now.getMonth(), 1).toISOString().split('T')[0]
      const thisMonthEnd = new Date(now.getFullYear(), now.getMonth() + 1, 0).toISOString().split('T')[0]
      const lastMonthStart = new Date(now.getFullYear(), now.getMonth() - 1, 1).toISOString().split('T')[0]
      const lastMonthEnd = new Date(now.getFullYear(), now.getMonth(), 0).toISOString().split('T')[0]
      const thisYearStart = new Date(now.getFullYear(), 0, 1).toISOString().split('T')[0]
      const thisYearEnd = new Date(now.getFullYear(), 11, 31).toISOString().split('T')[0]

      const [thisMonthExpenses, lastMonthExpenses, thisYearExpenses, budgets, categories] = await Promise.all([
        PersonalExpenseService.getExpensesByDateRange(thisMonthStart, thisMonthEnd),
        PersonalExpenseService.getExpensesByDateRange(lastMonthStart, lastMonthEnd),
        PersonalExpenseService.getExpensesByDateRange(thisYearStart, thisYearEnd),
        PersonalExpenseService.getPersonalBudgets(),
        PersonalExpenseService.getExpenseCategories()
      ])

      const thisMonth = thisMonthExpenses.reduce((sum, expense) => sum + expense.amount, 0)
      const lastMonth = lastMonthExpenses.reduce((sum, expense) => sum + expense.amount, 0)
      const thisYear = thisYearExpenses.reduce((sum, expense) => sum + expense.amount, 0)
      const averageMonthly = thisYear / (now.getMonth() + 1)

      // Find top category
      const categoryTotals = new Map<string, number>()
      thisYearExpenses.forEach(expense => {
        if (expense.category_id) {
          const current = categoryTotals.get(expense.category_id) || 0
          categoryTotals.set(expense.category_id, current + expense.amount)
        }
      })

      let topCategoryId = ''
      let topCategoryAmount = 0
      categoryTotals.forEach((amount, categoryId) => {
        if (amount > topCategoryAmount) {
          topCategoryAmount = amount
          topCategoryId = categoryId
        }
      })

      const topCategory = categories.find(cat => cat.id === topCategoryId)?.name || 'N/A'

      // Calculate budget utilization
      const totalBudget = budgets.reduce((sum, budget) => sum + budget.amount, 0)
      const budgetUtilization = totalBudget > 0 ? (thisMonth / totalBudget) * 100 : 0

      return {
        thisMonth,
        lastMonth,
        thisYear,
        averageMonthly,
        topCategory,
        budgetUtilization
      }
    } catch (error) {
      console.error('Error getting expense statistics:', error)
      return {
        thisMonth: 0,
        lastMonth: 0,
        thisYear: 0,
        averageMonthly: 0,
        topCategory: 'N/A',
        budgetUtilization: 0
      }
    }
  }
}
