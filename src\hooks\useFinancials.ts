import { useState, useEffect } from 'react';
import {
  FinancialService,
  ClientFinancial,
  CashFlowTransaction,
  CompanyAsset,
  PaymentRecord
} from '@/lib/financials';
import {
  CompanyExpense,
  CompanyExpenseCategory
} from '@/lib/companyExpenseServiceNew';



export const useFinancials = () => {
  const [clientFinancials, setClientFinancials] = useState<ClientFinancial[]>([]);
  const [expenses, setExpenses] = useState<CompanyExpense[]>([]);
  const [expenseCategories, setExpenseCategories] = useState<CompanyExpenseCategory[]>([]);
  const [cashFlowTransactions, setCashFlowTransactions] = useState<CashFlowTransaction[]>([]);
  const [companyAssets, setCompanyAssets] = useState<CompanyAsset[]>([]);
  const [paymentRecords, setPaymentRecords] = useState<PaymentRecord[]>([]);
  const [financialSummary, setFinancialSummary] = useState({
    totalRevenue: 0,
    totalExpenses: 0,
    netProfit: 0,
    totalInflow: 0,
    totalOutflow: 0,
    netCashFlow: 0,
    paidRevenue: 0,
    paidExpenses: 0,
    outstandingRevenue: 0,
    pendingExpenses: 0
  });
  const [cashFlowData, setCashFlowData] = useState<any[]>([]);
  const [cashFlowBreakdown, setCashFlowBreakdown] = useState<any[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Create default expense categories in database
  const createDefaultExpenseCategories = async () => {
    try {
      const categoriesToCreate = DEFAULT_EXPENSE_CATEGORIES.map(cat => ({
        name: cat.name,
        description: cat.description,
        is_project_related: cat.is_project_related
      }));

      const { error } = await supabase
        .from('expense_categories')
        .insert(categoriesToCreate);

      if (error) {
        console.error('Error creating default expense categories:', error);
      } else {
        console.log('✅ Default expense categories created');
      }
    } catch (error) {
      console.error('Error creating default expense categories:', error);
    }
  };

  // Calculate financial summary from current data
  const calculateFinancialSummary = () => {
    const totalExpenses = expenses.reduce((sum, expense) => sum + expense.amount, 0);
    // Don't override totalRevenue - it should come from the database
    // This function should only be used for local calculations when database is not available
    const netProfit = financialSummary.totalRevenue - totalExpenses;

    return {
      totalRevenue: financialSummary.totalRevenue, // Keep the database value
      totalExpenses,
      netProfit,
      totalInflow: financialSummary.totalRevenue,
      totalOutflow: totalExpenses,
      netCashFlow: financialSummary.totalRevenue - totalExpenses
    };
  };

  // Load all financial data
  const loadFinancialData = async () => {
    try {
      setLoading(true);
      setError(null);

      // Load from database
      try {
        console.log('Loading financial data from database...');

        const [
          clientFinancialsData,
          expensesData,
          expenseCategoriesData,
          cashFlowTransactionsData,
          assetsData,
          paymentsData,
          summaryData,
          cashFlowChartData,
          cashFlowBreakdownData
        ] = await Promise.all([
          FinancialService.getClientFinancials(),
          FinancialService.getExpenses(),
          FinancialService.getExpenseCategories(),
          FinancialService.getCashFlowTransactions(),
          FinancialService.getCompanyAssets(),
          FinancialService.getPaymentRecords(),
          FinancialService.getFinancialSummary(),
          FinancialService.getCashFlowData(),
          FinancialService.getCashFlowBreakdown()
        ]);

        console.log('Loaded data:', {
          expenses: expensesData.length,
          categories: expenseCategoriesData.length,
          cashFlowTransactions: cashFlowTransactionsData.length,
          cashFlowChartData: cashFlowChartData.length,
          cashFlowBreakdown: cashFlowBreakdownData.length,
          summary: summaryData
        });

        setClientFinancials(clientFinancialsData);
        setExpenses(expensesData);
        // If no categories exist in database, create default ones
        if (expenseCategoriesData.length === 0) {
          console.log('📋 No expense categories found, creating defaults...');
          await createDefaultExpenseCategories();
          // Reload categories after creation
          const { data: newCategories } = await FinancialService.getExpenseCategories();
          setExpenseCategories(newCategories || DEFAULT_EXPENSE_CATEGORIES);
        } else {
          setExpenseCategories(expenseCategoriesData);
        }
        setCashFlowTransactions(cashFlowTransactionsData);
        setCompanyAssets(assetsData);
        setPaymentRecords(paymentsData);
        setFinancialSummary(summaryData);
        setCashFlowData(cashFlowChartData);
        setCashFlowBreakdown(cashFlowBreakdownData);
      } catch (dbError) {
        console.error('Database error:', dbError);
        setError('Failed to load financial data from database');
        // Set default categories if database fails
        setExpenseCategories(DEFAULT_EXPENSE_CATEGORIES);
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to load financial data');
      console.error('Error loading financial data:', err);
    } finally {
      setLoading(false);
    }
  };

  // Client Financial operations
  const createClientFinancial = async (clientData: Partial<ClientFinancial>) => {
    try {
      const newClient = await FinancialService.createClientFinancial(clientData);
      if (newClient) {
        setClientFinancials(prev => [newClient, ...prev]);
        return newClient;
      }
      return null;
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to create client financial record');
      return null;
    }
  };

  const updateClientFinancial = async (id: string, updates: Partial<ClientFinancial>) => {
    try {
      const success = await FinancialService.updateClientFinancial(id, updates);
      if (success) {
        setClientFinancials(prev => 
          prev.map(client => client.id === id ? { ...client, ...updates } : client)
        );
        return true;
      }
      return false;
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to update client financial record');
      return false;
    }
  };

  // Expense operations
  const createExpense = async (expenseData: Partial<CompanyExpense>) => {
    try {
      console.log('Creating expense via hook:', expenseData);

      // Create expense in database
      const dbExpense = await FinancialService.createExpense(expenseData);
      if (dbExpense) {
        console.log('Expense created in database:', dbExpense);

        // Update local state immediately
        setExpenses(prev => [dbExpense, ...prev]);

        // If expense is paid, refresh all financial data to get updated cash flow
        if (dbExpense.status === 'paid') {
          console.log('Expense is paid, refreshing all financial data...');
          await loadFinancialData(); // Reload everything to get fresh cash flow data
        } else {
          // Just refresh the summary for pending expenses
          await refreshFinancialSummary();
        }

        return dbExpense;
      } else {
        throw new Error('Failed to create expense in database');
      }
    } catch (err) {
      console.error('Error creating expense:', err);
      setError(err instanceof Error ? err.message : 'Failed to create expense');
      return null;
    }
  };

  const updateExpense = async (id: string, updates: Partial<Expense>) => {
    try {
      const success = await FinancialService.updateExpense(id, updates);
      if (success) {
        setExpenses(prev => 
          prev.map(expense => expense.id === id ? { ...expense, ...updates } : expense)
        );
        // Update financial summary
        const updatedSummary = await FinancialService.getFinancialSummary();
        setFinancialSummary(updatedSummary);
        return true;
      }
      return false;
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to update expense');
      return false;
    }
  };

  const deleteExpense = async (id: string) => {
    try {
      const success = await FinancialService.deleteExpense(id);
      if (success) {
        setExpenses(prev => prev.filter(expense => expense.id !== id));
        // Update financial summary
        const updatedSummary = await FinancialService.getFinancialSummary();
        setFinancialSummary(updatedSummary);
        return true;
      }
      return false;
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to delete expense');
      return false;
    }
  };

  // Cash Flow operations
  const createCashFlowTransaction = async (transactionData: Partial<CashFlowTransaction>) => {
    try {
      const newTransaction = await FinancialService.createCashFlowTransaction(transactionData);
      if (newTransaction) {
        setCashFlowTransactions(prev => [newTransaction, ...prev]);
        // Update financial summary
        const updatedSummary = await FinancialService.getFinancialSummary();
        setFinancialSummary(updatedSummary);
        return newTransaction;
      }
      return null;
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to create cash flow transaction');
      return null;
    }
  };

  // Asset operations
  const createCompanyAsset = async (assetData: Partial<CompanyAsset>) => {
    try {
      const newAsset = await FinancialService.createCompanyAsset(assetData);
      if (newAsset) {
        setCompanyAssets(prev => [newAsset, ...prev]);
        return newAsset;
      }
      return null;
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to create company asset');
      return null;
    }
  };

  const updateCompanyAsset = async (id: string, updates: Partial<CompanyAsset>) => {
    try {
      const success = await FinancialService.updateCompanyAsset(id, updates);
      if (success) {
        setCompanyAssets(prev => 
          prev.map(asset => asset.id === id ? { ...asset, ...updates } : asset)
        );
        return true;
      }
      return false;
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to update company asset');
      return false;
    }
  };

  const deleteCompanyAsset = async (id: string) => {
    try {
      const success = await FinancialService.deleteCompanyAsset(id);
      if (success) {
        setCompanyAssets(prev => prev.filter(asset => asset.id !== id));
        return true;
      }
      return false;
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to delete company asset');
      return false;
    }
  };

  // Payment operations
  const createPaymentRecord = async (paymentData: Partial<PaymentRecord>) => {
    try {
      const newPayment = await FinancialService.createPaymentRecord(paymentData);
      if (newPayment) {
        setPaymentRecords(prev => [newPayment, ...prev]);
        // Update financial summary
        const updatedSummary = await FinancialService.getFinancialSummary();
        setFinancialSummary(updatedSummary);
        return newPayment;
      }
      return null;
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to create payment record');
      return null;
    }
  };

  // Refresh financial summary and cash flow data
  const refreshFinancialSummary = async (startDate?: string, endDate?: string) => {
    try {
      console.log('Refreshing financial summary with dates:', { startDate, endDate });

      const [summary, cashFlowChartData, cashFlowBreakdownData] = await Promise.all([
        FinancialService.getFinancialSummary(startDate, endDate),
        FinancialService.getCashFlowData(startDate, endDate),
        FinancialService.getCashFlowBreakdown(startDate, endDate)
      ]);

      console.log('Financial summary:', summary);
      console.log('Cash flow chart data:', cashFlowChartData);
      console.log('Cash flow breakdown data:', cashFlowBreakdownData);

      setFinancialSummary(summary);
      setCashFlowData(cashFlowChartData);
      setCashFlowBreakdown(cashFlowBreakdownData);
      return summary;
    } catch (err) {
      console.error('Error refreshing financial summary:', err);
      setError(err instanceof Error ? err.message : 'Failed to refresh financial summary');
      return null;
    }
  };

  // Get cash flow data for specific period
  const getCashFlowDataForPeriod = async (
    periodType: 'day' | 'week' | 'month' | 'year' = 'month',
    startDate?: string,
    endDate?: string
  ) => {
    try {
      const data = await FinancialService.getCashFlowData(startDate, endDate, periodType);
      return data;
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to get cash flow data');
      return [];
    }
  };

  // Update financial summary when expenses change
  useEffect(() => {
    // Only update if we have database data loaded, otherwise keep the database values
    if (financialSummary.totalRevenue > 0 || expenses.length > 0) {
      const totalExpenses = expenses.reduce((sum, expense) => sum + expense.amount, 0);
      setFinancialSummary(prev => ({
        ...prev,
        totalExpenses,
        netProfit: prev.totalRevenue - totalExpenses,
        totalOutflow: totalExpenses,
        netCashFlow: prev.totalRevenue - totalExpenses
      }));
    }
  }, [expenses]);

  // Load data on mount
  useEffect(() => {
    loadFinancialData();
  }, []);

  return {
    // Data
    clientFinancials,
    expenses,
    expenseCategories,
    cashFlowTransactions,
    companyAssets,
    paymentRecords,
    financialSummary,
    cashFlowData,
    cashFlowBreakdown,
    loading,
    error,

    // Operations
    loadFinancialData,
    createClientFinancial,
    updateClientFinancial,
    createExpense,
    updateExpense,
    deleteExpense,
    createCashFlowTransaction,
    createCompanyAsset,
    updateCompanyAsset,
    deleteCompanyAsset,
    createPaymentRecord,
    refreshFinancialSummary,
    getCashFlowDataForPeriod
  };
};
