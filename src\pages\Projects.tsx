
import React, { useState } from 'react';
import Layout from '@/components/layout/Layout';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Textarea } from '@/components/ui/textarea';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { FolderOpen, Plus, Edit, Trash2, DollarSign, Download, Printer, Search, Filter, AlertCircle, AlertTriangle, Wifi, WifiOff, RefreshCw, CheckCircle, XCircle, Calendar, MapPin, User, BarChart3, BarChart2, List, Grid, Target, Activity, Building } from 'lucide-react';
import { Badge } from '@/components/ui/badge';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Progress } from '@/components/ui/progress';
import { useToast } from '@/components/ui/use-toast';
import { useProjects } from '@/hooks/useProjects';
import { useTasks } from '@/hooks/useTasks';
import { ProjectService, PROJECT_STATUSES, PROJECT_PRIORITIES } from '@/lib/projects';
import { GanttChart } from '@/components/projects/GanttChart';
import { TaskKanban } from '@/components/projects/TaskKanban';
// import { ProjectTimeline } from '@/components/projects/ProjectTimeline';
import { TaskManager } from '@/components/projects/TaskManager';
import { ProjectAnalytics } from '@/components/projects/ProjectAnalytics';
import { CreatorInfo } from '@/components/ui/CreatorInfo';

import { ClientLink } from '@/components/shared/ClientLink';
import { useClients } from '@/hooks/useClients';

const Projects = () => {
  const { toast } = useToast();

  // Use the custom hook for project management with Supabase
  const {
    projects,
    loading,
    error,
    addProject,
    updateProject,
    deleteProject,
    refreshProjects,
    isOnline,
    syncStatus
  } = useProjects();

  // Use the custom hook for task management with Supabase
  const {
    tasks,
    loading: tasksLoading,
    error: tasksError,
    addTask,
    updateTask,
    deleteTask,
    refreshTasks,
    getTasksByProject,
    getTaskStats,
    isOnline: tasksOnline,
    syncStatus: tasksSyncStatus
  } = useTasks();

  // Use the custom hook for client management
  const { clients } = useClients();

  // Advanced features state
  const [activeTab, setActiveTab] = useState('overview');
  const [selectedProject, setSelectedProject] = useState<any>(null);

  // Sample milestones for demonstration
  const [milestones, setMilestones] = useState([
    {
      id: '1',
      name: 'Foundation Complete',
      date: '2024-02-15',
      status: 'completed' as const,
      project_id: projects[0]?.id || '',
      description: 'Foundation work completed and inspected'
    },
    {
      id: '2',
      name: 'Framing Complete',
      date: '2024-03-30',
      status: 'upcoming' as const,
      project_id: projects[0]?.id || '',
      description: 'Structural framing completion'
    },
    {
      id: '3',
      name: 'Electrical Rough-in',
      date: '2024-04-15',
      status: 'upcoming' as const,
      project_id: projects[1]?.id || '',
      description: 'Electrical rough-in work'
    },
    {
      id: '4',
      name: 'Final Inspection',
      date: '2024-06-01',
      status: 'upcoming' as const,
      project_id: projects[0]?.id || '',
      description: 'Final building inspection'
    }
  ]);

  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const [editingProject, setEditingProject] = useState<any>(null);
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedStatus, setSelectedStatus] = useState('all');
  const [selectedPriority, setSelectedPriority] = useState('all');
  const [formData, setFormData] = useState({
    name: '',
    description: '',
    status: 'Planning',
    priority: 'Medium',
    start_date: '',
    end_date: '',
    budget: '',
    spent: '',
    progress: '',
    client_name: '',
    project_manager: '',
    location: ''
  });
  const [formErrors, setFormErrors] = useState<Record<string, string>>({});
  const [isSubmitting, setIsSubmitting] = useState(false);

  // Filter projects based on search and filters
  const filteredProjects = projects.filter(project => {
    const matchesSearch = project.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         project.client_name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         project.project_manager.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesStatus = selectedStatus === 'all' || project.status === selectedStatus;
    const matchesPriority = selectedPriority === 'all' || project.priority === selectedPriority;

    return matchesSearch && matchesStatus && matchesPriority;
  });

  const totalBudget = filteredProjects.reduce((sum, project) => sum + Number(project.budget), 0);
  const totalSpent = filteredProjects.reduce((sum, project) => sum + Number(project.spent), 0);

  // Form validation
  const validateForm = () => {
    const errors: Record<string, string> = {};

    if (!formData.name.trim()) errors.name = 'Project name is required';
    if (!formData.description.trim()) errors.description = 'Description is required';
    if (!formData.client_name.trim()) errors.client_name = 'Client name is required';
    if (!formData.project_manager.trim()) errors.project_manager = 'Project manager is required';
    if (!formData.location.trim()) errors.location = 'Location is required';
    if (!formData.start_date) errors.start_date = 'Start date is required';
    if (!formData.end_date) errors.end_date = 'End date is required';
    if (!formData.budget || parseFloat(formData.budget) <= 0) errors.budget = 'Valid budget is required';
    if (!formData.spent || parseFloat(formData.spent) < 0) errors.spent = 'Spent amount must be 0 or positive';
    if (!formData.progress || parseInt(formData.progress) < 0 || parseInt(formData.progress) > 100) {
      errors.progress = 'Progress must be between 0 and 100';
    }

    setFormErrors(errors);
    return Object.keys(errors).length === 0;
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'Completed':
        return 'bg-green-100 dark:bg-green-900/30 text-green-800 dark:text-green-300';
      case 'In Progress':
        return 'bg-blue-100 dark:bg-blue-900/30 text-blue-800 dark:text-blue-300';
      case 'On Hold':
        return 'bg-yellow-100 dark:bg-yellow-900/30 text-yellow-800 dark:text-yellow-300';
      case 'Cancelled':
        return 'bg-red-100 dark:bg-red-900/30 text-red-800 dark:text-red-300';
      case 'Planning':
        return 'bg-purple-100 dark:bg-purple-900/30 text-purple-800 dark:text-purple-300';
      default:
        return 'bg-gray-100 dark:bg-gray-800 text-gray-800 dark:text-gray-300';
    }
  };

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'Critical':
        return 'bg-red-100 dark:bg-red-900/30 text-red-800 dark:text-red-300';
      case 'High':
        return 'bg-orange-100 dark:bg-orange-900/30 text-orange-800 dark:text-orange-300';
      case 'Medium':
        return 'bg-yellow-100 dark:bg-yellow-900/30 text-yellow-800 dark:text-yellow-300';
      case 'Low':
        return 'bg-green-100 dark:bg-green-900/30 text-green-800 dark:text-green-300';
      default:
        return 'bg-gray-100 dark:bg-gray-800 text-gray-800 dark:text-gray-300';
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    // Prevent double submission
    if (isSubmitting) {
      return;
    }

    if (!validateForm()) {
      toast({
        title: "Validation Error",
        description: "Please fix the errors in the form",
        variant: "destructive",
      });
      return;
    }

    setIsSubmitting(true);
    try {
      const budget = parseFloat(formData.budget);
      const spent = parseFloat(formData.spent);
      const progress = parseInt(formData.progress);

      if (editingProject) {
        await updateProject(editingProject.id, {
          name: formData.name.trim(),
          description: formData.description.trim(),
          status: formData.status,
          priority: formData.priority,
          start_date: formData.start_date,
          end_date: formData.end_date,
          budget,
          spent,
          progress,
          client_name: formData.client_name.trim(),
          project_manager: formData.project_manager.trim(),
          location: formData.location.trim()
        });
        toast({
          title: "Success",
          description: "Project updated successfully",
        });
      } else {
        await addProject({
          name: formData.name.trim(),
          description: formData.description.trim(),
          status: formData.status,
          priority: formData.priority,
          start_date: formData.start_date,
          end_date: formData.end_date,
          budget,
          spent,
          progress,
          client_name: formData.client_name.trim(),
          project_manager: formData.project_manager.trim(),
          location: formData.location.trim()
        });
        toast({
          title: "Success",
          description: "Project added successfully",
        });
      }

      resetForm();
      setIsDialogOpen(false);
    } catch (error) {
      toast({
        title: "Error",
        description: error instanceof Error ? error.message : "An error occurred",
        variant: "destructive",
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleEdit = (project: any) => {
    setEditingProject(project);
    setFormData({
      name: project.name,
      description: project.description,
      status: project.status,
      priority: project.priority,
      start_date: project.start_date,
      end_date: project.end_date,
      budget: project.budget.toString(),
      spent: project.spent.toString(),
      progress: project.progress.toString(),
      client_name: project.client_name,
      project_manager: project.project_manager,
      location: project.location
    });
    setIsDialogOpen(true);
  };

  const handleDelete = async (id: string) => {
    try {
      const project = projects.find(p => p.id === id);
      await deleteProject(id);
      toast({
        title: "Success",
        description: `${project?.name} removed from projects`,
      });
    } catch (error) {
      toast({
        title: "Error",
        description: error instanceof Error ? error.message : "Failed to delete project",
        variant: "destructive",
      });
    }
  };

  const resetForm = () => {
    setFormData({
      name: '',
      description: '',
      status: 'Planning',
      priority: 'Medium',
      start_date: '',
      end_date: '',
      budget: '',
      spent: '',
      progress: '',
      client_name: '',
      project_manager: '',
      location: ''
    });
    setFormErrors({});
    setEditingProject(null);
  };

  // Export functionality
  const exportToCSV = async () => {
    try {
      const csvContent = await ProjectService.exportToCSV();
      const blob = new Blob([csvContent], { type: 'text/csv' });
      const url = window.URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = `projects-${new Date().toISOString().split('T')[0]}.csv`;
      a.click();
      window.URL.revokeObjectURL(url);

      toast({
        title: "Success",
        description: "Projects exported successfully",
      });
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to export data",
        variant: "destructive",
      });
    }
  };

  // Print functionality
  const handlePrint = () => {
    window.print();
  };

  // Clear all filters
  const clearFilters = () => {
    setSearchTerm('');
    setSelectedStatus('all');
    setSelectedPriority('all');
  };

  // Get sync status icon
  const getSyncStatusIcon = () => {
    switch (syncStatus) {
      case 'syncing':
        return <RefreshCw className="w-4 h-4 animate-spin text-blue-500" />;
      case 'synced':
        return <CheckCircle className="w-4 h-4 text-green-500" />;
      case 'offline':
        return <WifiOff className="w-4 h-4 text-orange-500" />;
      case 'error':
        return <XCircle className="w-4 h-4 text-red-500" />;
      default:
        return <Wifi className="w-4 h-4 text-gray-500" />;
    }
  };

  // Task management functions
  const handleTaskCreate = async (taskData: any) => {
    try {
      await addTask(taskData);
      toast({
        title: "Task Created",
        description: `${taskData.name} has been created successfully.`,
      });
    } catch (error) {
      toast({
        title: "Error",
        description: error instanceof Error ? error.message : "Failed to create task. Please try again.",
        variant: "destructive",
      });
    }
  };

  const handleTaskUpdate = async (taskId: string, updates: any) => {
    try {
      await updateTask(taskId, updates);
      toast({
        title: "Task Updated",
        description: "Task has been updated successfully.",
      });
    } catch (error) {
      toast({
        title: "Error",
        description: error instanceof Error ? error.message : "Failed to update task. Please try again.",
        variant: "destructive",
      });
    }
  };

  const handleTaskDelete = async (taskId: string) => {
    try {
      await deleteTask(taskId);
      toast({
        title: "Task Deleted",
        description: "Task has been deleted successfully.",
      });
    } catch (error) {
      toast({
        title: "Error",
        description: error instanceof Error ? error.message : "Failed to delete task. Please try again.",
        variant: "destructive",
      });
    }
  };

  // Project update for Kanban
  const handleProjectUpdate = async (projectId: string, updates: any) => {
    try {
      await updateProject(projectId, updates);
      toast({
        title: "Project Updated",
        description: "Project status has been updated successfully.",
      });
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to update project. Please try again.",
        variant: "destructive",
      });
    }
  };

  // Open new project dialog
  const handleProjectCreate = () => {
    resetForm();
    setIsDialogOpen(true);
  };

  return (
    <Layout>
      <div className="space-y-6">
        {/* Connection Status Alert */}
        {error && (
          <Alert variant="destructive">
            <AlertCircle className="h-4 w-4" />
            <AlertDescription>
              {error} - Using offline mode. Changes will sync when connection is restored.
            </AlertDescription>
          </Alert>
        )}

        {/* Tasks Error Alert */}
        {tasksError && (
          <Alert variant="destructive">
            <AlertCircle className="h-4 w-4" />
            <AlertDescription>
              Tasks: {tasksError} - Task changes will sync when connection is restored.
            </AlertDescription>
          </Alert>
        )}

        {/* Loading State */}
        {(loading || tasksLoading) && (
          <Alert>
            <RefreshCw className="h-4 w-4 animate-spin" />
            <AlertDescription>
              Loading {loading && tasksLoading ? 'project and task' : loading ? 'project' : 'task'} data...
            </AlertDescription>
          </Alert>
        )}

        {/* Empty State Alert */}
        {!loading && projects.length === 0 && (
          <Alert>
            <AlertCircle className="h-4 w-4" />
            <AlertDescription>
              Welcome to Project Management! Start by adding your first project to begin tracking construction work.
            </AlertDescription>
          </Alert>
        )}

        {/* Header Section */}
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-3">
            <div className="w-12 h-12 bg-gradient-to-r from-blue-600 to-purple-600 rounded-xl flex items-center justify-center">
              <FolderOpen className="w-6 h-6 text-white" />
            </div>
            <div>
              <h1 className="text-3xl font-bold text-gray-900 dark:text-white font-heading">Project Management</h1>
              <div className="flex items-center space-x-2">
                <p className="text-gray-600">Manage construction projects and track progress</p>
                <div className="flex items-center space-x-1">
                  {getSyncStatusIcon()}
                  <span className="text-xs text-gray-500">
                    Projects: {syncStatus === 'offline' ? 'Offline' :
                     syncStatus === 'syncing' ? 'Syncing...' :
                     syncStatus === 'error' ? 'Error' : 'Online'}
                  </span>
                  <span className="text-xs text-gray-400">|</span>
                  <span className="text-xs text-gray-500">
                    Tasks: {tasksSyncStatus === 'offline' ? 'Offline' :
                     tasksSyncStatus === 'syncing' ? 'Syncing...' :
                     tasksSyncStatus === 'error' ? 'Error' : 'Online'}
                  </span>
                </div>
              </div>
            </div>
          </div>
          <div className="flex items-center space-x-2">
            <Button
              onClick={() => {
                console.log('Manual refresh disabled to prevent loops');
                // Temporarily disabled: await Promise.all([refreshProjects(), refreshTasks()]);
              }}
              variant="outline"
              size="sm"
              disabled={true}
            >
              <RefreshCw className="w-4 h-4 mr-2" />
              Refresh (Disabled)
            </Button>
            <Button onClick={exportToCSV} variant="outline" className="hidden sm:flex">
              <Download className="w-4 h-4 mr-2" />
              Export CSV
            </Button>
            <Button onClick={handlePrint} variant="outline" className="hidden sm:flex">
              <Printer className="w-4 h-4 mr-2" />
              Print
            </Button>
            <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
              <DialogTrigger asChild>
                <Button onClick={resetForm} className="bg-blue-600 hover:bg-blue-700">
                  <Plus className="w-4 h-4 mr-2" />
                  Add Project
                </Button>
              </DialogTrigger>
              <DialogContent className="sm:max-w-[600px] max-h-[90vh] overflow-y-auto">
                <DialogHeader>
                  <DialogTitle>
                    {editingProject ? 'Edit Project' : 'Add New Project'}
                  </DialogTitle>
                  <DialogDescription>
                    {editingProject ? 'Update project information and progress.' : 'Enter project details for tracking and management.'}
                  </DialogDescription>
                </DialogHeader>
                <form onSubmit={handleSubmit}>
                  <div className="grid gap-4 py-4">
                    <div className="grid grid-cols-2 gap-4">
                      <div className="space-y-2">
                        <Label htmlFor="name">Project Name</Label>
                        <Input
                          id="name"
                          value={formData.name}
                          onChange={(e) => setFormData({...formData, name: e.target.value})}
                          required
                        />
                        {formErrors.name && (
                          <span className="text-sm text-red-500">{formErrors.name}</span>
                        )}
                      </div>
                      <div className="space-y-2">
                        <Label htmlFor="client_name">Client</Label>
                        <Select
                          value={formData.client_name}
                          onValueChange={(value) => setFormData({...formData, client_name: value})}
                        >
                          <SelectTrigger>
                            <SelectValue placeholder="Select a client" />
                          </SelectTrigger>
                          <SelectContent>
                            {clients.map((client, index) => (
                              <SelectItem key={`${client.id}-${index}`} value={client.name}>
                                <div className="flex items-center space-x-2">
                                  <Building className="w-4 h-4" />
                                  <span>{client.name}</span>
                                  {client.company_name && client.company_name !== client.name && (
                                    <span className="text-gray-500">({client.company_name})</span>
                                  )}
                                </div>
                              </SelectItem>
                            ))}
                            <SelectItem value="__new_client__">
                              <div className="flex items-center space-x-2 text-blue-600">
                                <Plus className="w-4 h-4" />
                                <span>Add New Client</span>
                              </div>
                            </SelectItem>
                          </SelectContent>
                        </Select>
                        {formData.client_name === '__new_client__' && (
                          <Input
                            placeholder="Enter new client name"
                            value={formData.client_name === '__new_client__' ? '' : formData.client_name}
                            onChange={(e) => setFormData({...formData, client_name: e.target.value})}
                            required
                          />
                        )}
                        {formErrors.client_name && (
                          <span className="text-sm text-red-500">{formErrors.client_name}</span>
                        )}
                      </div>
                    </div>

                    <div className="space-y-2">
                      <Label htmlFor="description">Description</Label>
                      <Textarea
                        id="description"
                        value={formData.description}
                        onChange={(e) => setFormData({...formData, description: e.target.value})}
                        rows={3}
                        required
                      />
                      {formErrors.description && (
                        <span className="text-sm text-red-500">{formErrors.description}</span>
                      )}
                    </div>

                    <div className="grid grid-cols-2 gap-4">
                      <div className="space-y-2">
                        <Label htmlFor="status">Status</Label>
                        <Select value={formData.status} onValueChange={(value) => setFormData({...formData, status: value})}>
                          <SelectTrigger>
                            <SelectValue placeholder="Select status" />
                          </SelectTrigger>
                          <SelectContent>
                            {PROJECT_STATUSES.map((status) => (
                              <SelectItem key={status} value={status}>
                                {status}
                              </SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                      </div>
                      <div className="space-y-2">
                        <Label htmlFor="priority">Priority</Label>
                        <Select value={formData.priority} onValueChange={(value) => setFormData({...formData, priority: value})}>
                          <SelectTrigger>
                            <SelectValue placeholder="Select priority" />
                          </SelectTrigger>
                          <SelectContent>
                            {PROJECT_PRIORITIES.map((priority) => (
                              <SelectItem key={priority} value={priority}>
                                {priority}
                              </SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                      </div>
                    </div>

                    <div className="grid grid-cols-2 gap-4">
                      <div className="space-y-2">
                        <Label htmlFor="start_date">Start Date</Label>
                        <Input
                          id="start_date"
                          type="date"
                          value={formData.start_date}
                          onChange={(e) => setFormData({...formData, start_date: e.target.value})}
                          required
                        />
                        {formErrors.start_date && (
                          <span className="text-sm text-red-500">{formErrors.start_date}</span>
                        )}
                      </div>
                      <div className="space-y-2">
                        <Label htmlFor="end_date">End Date</Label>
                        <Input
                          id="end_date"
                          type="date"
                          value={formData.end_date}
                          onChange={(e) => setFormData({...formData, end_date: e.target.value})}
                          required
                        />
                        {formErrors.end_date && (
                          <span className="text-sm text-red-500">{formErrors.end_date}</span>
                        )}
                      </div>
                    </div>

                    <div className="grid grid-cols-3 gap-4">
                      <div className="space-y-2">
                        <Label htmlFor="budget">Budget ($)</Label>
                        <Input
                          id="budget"
                          type="number"
                          step="0.01"
                          value={formData.budget}
                          onChange={(e) => setFormData({...formData, budget: e.target.value})}
                          required
                          min="0"
                        />
                        {formErrors.budget && (
                          <span className="text-sm text-red-500">{formErrors.budget}</span>
                        )}
                      </div>
                      <div className="space-y-2">
                        <Label htmlFor="spent">Spent ($)</Label>
                        <Input
                          id="spent"
                          type="number"
                          step="0.01"
                          value={formData.spent}
                          onChange={(e) => setFormData({...formData, spent: e.target.value})}
                          required
                          min="0"
                        />
                        {formErrors.spent && (
                          <span className="text-sm text-red-500">{formErrors.spent}</span>
                        )}
                      </div>
                      <div className="space-y-2">
                        <Label htmlFor="progress">Progress (%)</Label>
                        <Input
                          id="progress"
                          type="number"
                          value={formData.progress}
                          onChange={(e) => setFormData({...formData, progress: e.target.value})}
                          required
                          min="0"
                          max="100"
                        />
                        {formErrors.progress && (
                          <span className="text-sm text-red-500">{formErrors.progress}</span>
                        )}
                      </div>
                    </div>

                    <div className="grid grid-cols-2 gap-4">
                      <div className="space-y-2">
                        <Label htmlFor="project_manager">Project Manager</Label>
                        <Input
                          id="project_manager"
                          value={formData.project_manager}
                          onChange={(e) => setFormData({...formData, project_manager: e.target.value})}
                          required
                        />
                        {formErrors.project_manager && (
                          <span className="text-sm text-red-500">{formErrors.project_manager}</span>
                        )}
                      </div>
                      <div className="space-y-2">
                        <Label htmlFor="location">Location</Label>
                        <Input
                          id="location"
                          value={formData.location}
                          onChange={(e) => setFormData({...formData, location: e.target.value})}
                          required
                        />
                        {formErrors.location && (
                          <span className="text-sm text-red-500">{formErrors.location}</span>
                        )}
                      </div>
                    </div>
                  </div>
                  <DialogFooter>
                    <Button type="submit" disabled={isSubmitting}>
                      {isSubmitting ? (
                        <>
                          <RefreshCw className="w-4 h-4 mr-2 animate-spin" />
                          {editingProject ? 'Updating...' : 'Adding...'}
                        </>
                      ) : (
                        editingProject ? 'Update Project' : 'Add Project'
                      )}
                    </Button>
                  </DialogFooter>
                </form>
              </DialogContent>
            </Dialog>
          </div>
        </div>

        {/* Search and Filter Section */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <Search className="w-5 h-5" />
              <span>Search & Filter</span>
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
                <Input
                  placeholder="Search projects, clients, or managers..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10"
                />
              </div>
              <Select value={selectedStatus} onValueChange={setSelectedStatus}>
                <SelectTrigger>
                  <SelectValue placeholder="Filter by status" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Statuses</SelectItem>
                  {PROJECT_STATUSES.map((status) => (
                    <SelectItem key={status} value={status}>
                      {status}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
              <Select value={selectedPriority} onValueChange={setSelectedPriority}>
                <SelectTrigger>
                  <SelectValue placeholder="Filter by priority" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Priorities</SelectItem>
                  {PROJECT_PRIORITIES.map((priority) => (
                    <SelectItem key={priority} value={priority}>
                      {priority}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
              <Button onClick={clearFilters} variant="outline" className="w-full">
                <Filter className="w-4 h-4 mr-2" />
                Clear Filters
              </Button>
            </div>
            {(searchTerm || (selectedStatus && selectedStatus !== 'all') || (selectedPriority && selectedPriority !== 'all')) && (
              <div className="mt-4 p-3 bg-blue-50 dark:bg-blue-900/20 rounded-lg">
                <p className="text-sm text-blue-700 dark:text-blue-300">
                  Showing {filteredProjects.length} of {projects.length} projects
                  {searchTerm && ` matching "${searchTerm}"`}
                  {selectedStatus && selectedStatus !== 'all' && ` with status "${selectedStatus}"`}
                  {selectedPriority && selectedPriority !== 'all' && ` with priority "${selectedPriority}"`}
                </p>
              </div>
            )}
          </CardContent>
        </Card>

        {/* Advanced Project Management Tabs */}
        <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
          <TabsList className="grid w-full grid-cols-7">
            <TabsTrigger value="overview" className="flex items-center space-x-2">
              <List className="w-4 h-4" />
              <span>Overview</span>
            </TabsTrigger>
            <TabsTrigger value="kanban" className="flex items-center space-x-2">
              <Grid className="w-4 h-4" />
              <span>Kanban</span>
            </TabsTrigger>
            <TabsTrigger value="gantt" className="flex items-center space-x-2">
              <BarChart2 className="w-4 h-4" />
              <span>Gantt</span>
            </TabsTrigger>
            <TabsTrigger value="timeline" className="flex items-center space-x-2">
              <Calendar className="w-4 h-4" />
              <span>Timeline</span>
            </TabsTrigger>
            <TabsTrigger value="tasks" className="flex items-center space-x-2">
              <Target className="w-4 h-4" />
              <span>Tasks</span>
            </TabsTrigger>
            <TabsTrigger value="analytics" className="flex items-center space-x-2">
              <BarChart3 className="w-4 h-4" />
              <span>Analytics</span>
            </TabsTrigger>

          </TabsList>

          {/* Overview Tab - Traditional Table View */}
          <TabsContent value="overview" className="space-y-6">
            {/* Summary Cards */}
            <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
              <Card>
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                  <CardTitle className="text-sm font-medium">Total Projects</CardTitle>
                  <FolderOpen className="h-4 w-4 text-muted-foreground" />
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold">{projects.length}</div>
                  <p className="text-xs text-muted-foreground">
                    Active projects
                  </p>
                </CardContent>
              </Card>
              <Card>
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                  <CardTitle className="text-sm font-medium">Filtered Results</CardTitle>
                  <Filter className="h-4 w-4 text-muted-foreground" />
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold">{filteredProjects.length}</div>
                  <p className="text-xs text-muted-foreground">
                    Projects matching filters
                  </p>
                </CardContent>
              </Card>
              <Card>
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                  <CardTitle className="text-sm font-medium">Total Budget</CardTitle>
                  <DollarSign className="h-4 w-4 text-muted-foreground" />
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold">${totalBudget.toLocaleString()}</div>
                  <p className="text-xs text-muted-foreground">
                    Combined budget (filtered)
                  </p>
                </CardContent>
              </Card>
              <Card>
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                  <CardTitle className="text-sm font-medium">Total Spent</CardTitle>
                  <DollarSign className="h-4 w-4 text-muted-foreground" />
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold">${totalSpent.toLocaleString()}</div>
                  <p className="text-xs text-muted-foreground">
                    Total expenditure (filtered)
                  </p>
                </CardContent>
              </Card>
            </div>

            {/* Projects Table */}
            <Card>
              <CardHeader>
                <CardTitle>Project Management</CardTitle>
                <CardDescription>
                  Complete list of construction projects with progress tracking
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="rounded-md border">
                  <Table>
                    <TableHeader>
                      <TableRow>
                        <TableHead>Project Name</TableHead>
                        <TableHead>Client</TableHead>
                        <TableHead>Status</TableHead>
                        <TableHead>Priority</TableHead>
                        <TableHead>Progress</TableHead>
                        <TableHead>Created By</TableHead>
                        <TableHead>Timeline</TableHead>
                        <TableHead className="text-right">Budget</TableHead>
                        <TableHead className="text-right">Spent</TableHead>
                        <TableHead className="text-center">Actions</TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      {filteredProjects.length === 0 ? (
                        <TableRow>
                          <TableCell colSpan={10} className="text-center py-8 text-gray-500">
                            {projects.length === 0
                              ? "No projects added yet. Click \"Add Project\" to get started."
                              : "No projects match the current filters. Try adjusting your search criteria."
                            }
                          </TableCell>
                        </TableRow>
                      ) : (
                        filteredProjects.map((project) => (
                          <TableRow key={project.id}>
                            <TableCell className="font-medium">
                              <div>
                                <div className="font-semibold">{project.name}</div>
                                <div className="text-sm text-gray-500 flex items-center">
                                  <MapPin className="w-3 h-3 mr-1" />
                                  {project.location}
                                </div>
                              </div>
                            </TableCell>
                            <TableCell>
                              <ClientLink
                                clientName={project.client_name}
                                variant="badge"
                              />
                            </TableCell>
                            <TableCell>
                              <Badge className={getStatusColor(project.status)}>{project.status}</Badge>
                            </TableCell>
                            <TableCell>
                              <Badge className={getPriorityColor(project.priority)}>{project.priority}</Badge>
                            </TableCell>
                            <TableCell>
                              <div className="flex items-center space-x-2">
                                <div className="w-16 bg-gray-200 dark:bg-gray-700 rounded-full h-2">
                                  <div
                                    className="bg-blue-600 h-2 rounded-full transition-all"
                                    style={{ width: `${project.progress}%` }}
                                  ></div>
                                </div>
                                <span className="text-sm font-medium">{project.progress}%</span>
                              </div>
                            </TableCell>
                            <TableCell>
                              <CreatorInfo
                                createdByName={project.created_by_name}
                                createdByAvatar={project.created_by_avatar}
                                createdAt={project.created_at}
                                variant="compact"
                                showLabel={false}
                                showDate={false}
                              />
                            </TableCell>
                            <TableCell>
                              <div className="text-sm">
                                <div className="flex items-center text-gray-600">
                                  <Calendar className="w-3 h-3 mr-1" />
                                  {new Date(project.start_date).toLocaleDateString()}
                                </div>
                                <div className="text-gray-500">
                                  to {new Date(project.end_date).toLocaleDateString()}
                                </div>
                              </div>
                            </TableCell>
                            <TableCell className="text-right font-semibold">
                              ${Number(project.budget).toLocaleString()}
                            </TableCell>
                            <TableCell className="text-right">
                              ${Number(project.spent).toLocaleString()}
                            </TableCell>
                            <TableCell className="text-center">
                              <div className="flex justify-center space-x-2">
                                <Button
                                  variant="outline"
                                  size="sm"
                                  onClick={() => handleEdit(project)}
                                >
                                  <Edit className="w-4 h-4" />
                                </Button>
                                <Button
                                  variant="outline"
                                  size="sm"
                                  onClick={() => handleDelete(project.id)}
                                  className="text-red-600 hover:text-red-700"
                                >
                                  <Trash2 className="w-4 h-4" />
                                </Button>
                              </div>
                            </TableCell>
                          </TableRow>
                        ))
                      )}
                    </TableBody>
                  </Table>
                </div>

                {/* Summary Row */}
                {filteredProjects.length > 0 && (
                  <div className="mt-4 p-4 bg-gray-50 dark:bg-gray-800 rounded-lg">
                    <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                      <div className="text-center">
                        <div className="text-lg font-semibold text-gray-900 dark:text-white">
                          {filteredProjects.length} Projects
                        </div>
                        <div className="text-sm text-gray-600">
                          {filteredProjects.length !== projects.length ? `of ${projects.length} total` : 'total'}
                        </div>
                      </div>
                      <div className="text-center">
                        <div className="text-lg font-semibold text-blue-600">
                          ${totalBudget.toLocaleString()}
                        </div>
                        <div className="text-sm text-gray-600">Total Budget</div>
                      </div>
                      <div className="text-center">
                        <div className="text-lg font-semibold text-green-600">
                          ${totalSpent.toLocaleString()}
                        </div>
                        <div className="text-sm text-gray-600">Total Spent</div>
                      </div>
                    </div>
                  </div>
                )}
              </CardContent>
            </Card>
          </TabsContent>

          {/* Kanban Tab - Advanced Board */}
          <TabsContent value="kanban" className="space-y-6">
            <TaskKanban
              projects={filteredProjects}
              tasks={tasks}
              loading={loading || tasksLoading}
              onTaskUpdate={handleTaskUpdate}
              onTaskCreate={handleTaskCreate}
              onTaskDelete={handleTaskDelete}
            />
          </TabsContent>

          {/* Gantt Chart Tab */}
          <TabsContent value="gantt" className="space-y-6">
            <GanttChart
              projects={projects}
              tasks={tasks}
              loading={loading || tasksLoading}
              onTaskUpdate={handleTaskUpdate}
              onTaskCreate={handleTaskCreate}
              onTaskDelete={handleTaskDelete}
            />
          </TabsContent>

          {/* Timeline Tab */}
          <TabsContent value="timeline" className="space-y-6">
            {projects.length === 0 ? (
              <Card>
                <CardContent className="text-center py-12">
                  <Calendar className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                  <h3 className="text-lg font-medium text-gray-900 mb-2">No Projects Available</h3>
                  <p className="text-gray-600 mb-4">
                    Create a project first to view the timeline.
                  </p>
                  <Button onClick={handleProjectCreate}>
                    <Plus className="w-4 h-4 mr-2" />
                    Create Project
                  </Button>
                </CardContent>
              </Card>
            ) : (
              <Card>
                <CardHeader>
                  <div className="flex items-center justify-between">
                    <div>
                      <CardTitle className="flex items-center gap-2">
                        <Calendar className="h-6 w-6 text-blue-600" />
                        📅 Visual Project Timeline
                      </CardTitle>
                      <CardDescription>
                        Interactive timeline with milestones, progress tracking, and project scheduling
                      </CardDescription>
                    </div>
                    <div className="flex items-center gap-2">
                      <Badge variant="secondary" className="bg-green-100 text-green-800">
                        ✅ {projects.length} Projects
                      </Badge>
                      <Badge variant="outline">
                        {milestones.length} Milestones
                      </Badge>
                    </div>
                  </div>
                </CardHeader>
                <CardContent>
                  <div className="space-y-6">
                    {/* Timeline Overview */}
                    <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                      <Card>
                        <CardContent className="p-4">
                          <div className="flex items-center gap-2 mb-2">
                            <div className="w-3 h-3 bg-blue-500 rounded-full"></div>
                            <span className="text-sm font-medium">Active Projects</span>
                          </div>
                          <p className="text-2xl font-bold">{projects.filter(p => p.status === 'In Progress').length}</p>
                        </CardContent>
                      </Card>
                      <Card>
                        <CardContent className="p-4">
                          <div className="flex items-center gap-2 mb-2">
                            <div className="w-3 h-3 bg-green-500 rounded-full"></div>
                            <span className="text-sm font-medium">Completed</span>
                          </div>
                          <p className="text-2xl font-bold">{projects.filter(p => p.status === 'Completed').length}</p>
                        </CardContent>
                      </Card>
                      <Card>
                        <CardContent className="p-4">
                          <div className="flex items-center gap-2 mb-2">
                            <div className="w-3 h-3 bg-red-500 rounded-full"></div>
                            <span className="text-sm font-medium">Milestones</span>
                          </div>
                          <p className="text-2xl font-bold">{milestones.length}</p>
                        </CardContent>
                      </Card>
                    </div>

                    {/* Project Timeline Cards */}
                    <div className="space-y-4">
                      <h3 className="text-lg font-medium">Project Timeline Overview</h3>
                      {projects.map((project) => {
                        const startDate = new Date(project.start_date);
                        const endDate = new Date(project.end_date);
                        const today = new Date();
                        const totalDays = Math.ceil((endDate.getTime() - startDate.getTime()) / (1000 * 60 * 60 * 24));
                        const elapsedDays = Math.ceil((today.getTime() - startDate.getTime()) / (1000 * 60 * 60 * 24));
                        const timeProgress = Math.max(0, Math.min(100, (elapsedDays / totalDays) * 100));

                        return (
                          <Card key={project.id} className="p-4">
                            <div className="flex justify-between items-start mb-4">
                              <div>
                                <h4 className="font-medium text-lg">{project.name}</h4>
                                <div className="mt-1">
                                  <ClientLink
                                    clientName={project.client_name}
                                    variant="link"
                                    className="text-sm"
                                  />
                                </div>
                                <p className="text-xs text-gray-500 mt-1">{project.description}</p>
                              </div>
                              <div className="flex items-center gap-2">
                                <Badge className={
                                  project.status === 'Completed' ? 'bg-green-100 text-green-800' :
                                  project.status === 'In Progress' ? 'bg-blue-100 text-blue-800' :
                                  project.status === 'On Hold' ? 'bg-yellow-100 text-yellow-800' :
                                  'bg-gray-100 text-gray-800'
                                }>
                                  {project.status}
                                </Badge>
                                <Badge variant="outline">
                                  {project.priority}
                                </Badge>
                              </div>
                            </div>

                            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                              <div>
                                <div className="flex justify-between text-sm mb-1">
                                  <span>Project Progress</span>
                                  <span>{project.progress}%</span>
                                </div>
                                <Progress value={project.progress} className="h-2 mb-2" />

                                <div className="flex justify-between text-sm mb-1">
                                  <span>Timeline Progress</span>
                                  <span>{Math.round(timeProgress)}%</span>
                                </div>
                                <Progress value={timeProgress} className="h-2" />
                              </div>

                              <div className="space-y-2 text-sm">
                                <div className="flex justify-between">
                                  <span className="text-gray-600">Start Date:</span>
                                  <span>{startDate.toLocaleDateString()}</span>
                                </div>
                                <div className="flex justify-between">
                                  <span className="text-gray-600">End Date:</span>
                                  <span>{endDate.toLocaleDateString()}</span>
                                </div>
                                <div className="flex justify-between">
                                  <span className="text-gray-600">Duration:</span>
                                  <span>{totalDays} days</span>
                                </div>
                                <div className="flex justify-between">
                                  <span className="text-gray-600">Budget:</span>
                                  <span>${project.budget.toLocaleString()}</span>
                                </div>
                              </div>
                            </div>

                            {/* Project Milestones */}
                            {milestones.filter(m => m.project_id === project.id).length > 0 && (
                              <div className="mt-4 pt-4 border-t">
                                <h5 className="text-sm font-medium mb-2">Milestones</h5>
                                <div className="flex flex-wrap gap-2">
                                  {milestones
                                    .filter(m => m.project_id === project.id)
                                    .map((milestone) => (
                                      <Badge
                                        key={milestone.id}
                                        variant="outline"
                                        className={
                                          milestone.status === 'completed' ? 'bg-green-50 border-green-200 text-green-800' :
                                          milestone.status === 'overdue' ? 'bg-red-50 border-red-200 text-red-800' :
                                          'bg-blue-50 border-blue-200 text-blue-800'
                                        }
                                      >
                                        {milestone.name} - {new Date(milestone.date).toLocaleDateString()}
                                      </Badge>
                                    ))}
                                </div>
                              </div>
                            )}
                          </Card>
                        );
                      })}
                    </div>
                  </div>
                </CardContent>
              </Card>
            )}
          </TabsContent>

          {/* Tasks Tab */}
          <TabsContent value="tasks" className="space-y-6">
            {projects.length === 0 ? (
              <Card>
                <CardContent className="text-center py-12">
                  <Target className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                  <h3 className="text-lg font-medium text-gray-900 mb-2">No Projects Available</h3>
                  <p className="text-gray-600 mb-4">
                    Create a project first to start managing tasks.
                  </p>
                  <Button onClick={handleProjectCreate}>
                    <Plus className="w-4 h-4 mr-2" />
                    Create Project
                  </Button>
                </CardContent>
              </Card>
            ) : (
              <div className="space-y-6">
                {/* Project Selector */}
                <Card>
                  <CardContent className="p-4">
                    <div className="flex items-center gap-4">
                      <Label className="text-sm font-medium">Select Project:</Label>
                      <Select
                        value={selectedProject?.id || projects[0]?.id || ''}
                        onValueChange={(value) => {
                          const project = projects.find(p => p.id === value);
                          setSelectedProject(project);
                        }}
                      >
                        <SelectTrigger className="w-64">
                          <SelectValue placeholder="Choose a project" />
                        </SelectTrigger>
                        <SelectContent>
                          {projects.map(project => (
                            <SelectItem key={project.id} value={project.id}>
                              {project.name}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                      {(selectedProject || projects[0]) && (
                        <Badge className={getStatusColor((selectedProject || projects[0]).status)}>
                          {(selectedProject || projects[0]).status}
                        </Badge>
                      )}
                    </div>
                  </CardContent>
                </Card>

                {/* Task Manager */}
                <TaskManager
                  projectId={selectedProject?.id || projects[0]?.id || ''}
                  tasks={getTasksByProject(selectedProject?.id || projects[0]?.id || '')}
                  onTaskCreate={handleTaskCreate}
                  onTaskUpdate={handleTaskUpdate}
                  onTaskDelete={handleTaskDelete}
                />
              </div>
            )}
          </TabsContent>

          {/* Analytics Tab */}
          <TabsContent value="analytics" className="space-y-6">
            <ProjectAnalytics
              projects={projects}
              tasks={tasks}
            />
          </TabsContent>


        </Tabs>
      </div>
    </Layout>
  );
};

export default Projects;
