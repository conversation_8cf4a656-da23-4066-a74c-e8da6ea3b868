import React, { useState, useEffect } from 'react'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { <PERSON>bs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { Badge } from '@/components/ui/badge'
import { Progress } from '@/components/ui/progress'
import { 
  FileText, 
  Download, 
  TrendingUp, 
  TrendingDown, 
  DollarSign, 
  PieChart,
  Calendar,
  Target,
  AlertTriangle,
  CheckCircle,
  BarChart3,
  Filter
} from 'lucide-react'
import { useToast } from '@/components/ui/use-toast'
import { 
  PersonalExpenseReportService, 
  ExpenseReport, 
  CategoryBreakdown, 
  BudgetBreakdown 
} from '@/lib/personalExpenseReportService'
import { PersonalExpensePdfService } from '@/lib/personalExpensePdfService'
import { PersonalExpenseService, PersonalExpense } from '@/lib/personalExpenseService'

export default function PersonalExpenseReports() {
  const { toast } = useToast()
  const [loading, setLoading] = useState(false)
  const [report, setReport] = useState<ExpenseReport | null>(null)
  const [expenses, setExpenses] = useState<PersonalExpense[]>([])
  const [statistics, setStatistics] = useState<any>(null)
  
  // Report filters
  const [startDate, setStartDate] = useState(() => {
    const date = new Date()
    date.setMonth(date.getMonth() - 1)
    return date.toISOString().split('T')[0]
  })
  const [endDate, setEndDate] = useState(() => new Date().toISOString().split('T')[0])
  const [reportType, setReportType] = useState('comprehensive')

  useEffect(() => {
    console.log('PersonalExpenseReports component mounted')
    loadStatistics()
    generateReport()
  }, [])

  const loadStatistics = async () => {
    try {
      console.log('Loading expense statistics...')
      const stats = await PersonalExpenseReportService.getExpenseStatistics()
      console.log('Statistics loaded:', stats)
      setStatistics(stats)
    } catch (error) {
      console.error('Error loading statistics:', error)
      toast({
        title: "Error",
        description: "Failed to load expense statistics",
        variant: "destructive"
      })
    }
  }

  const generateReport = async () => {
    setLoading(true)
    try {
      console.log('Generating report for period:', startDate, 'to', endDate)

      const reportData = await PersonalExpenseReportService.generateExpenseReport(startDate, endDate)
      console.log('Report data generated:', reportData)
      setReport(reportData)

      // Also load expenses for the period
      const expenseData = await PersonalExpenseService.getExpensesByDateRange(startDate, endDate)
      console.log('Expense data loaded:', expenseData.length, 'expenses')
      setExpenses(expenseData)

      toast({
        title: "Report Generated",
        description: `Report generated with ${expenseData.length} expenses and ${reportData.budgetBreakdown.length} budgets`
      })
    } catch (error) {
      console.error('Error generating report:', error)
      toast({
        title: "Error",
        description: `Failed to generate report: ${error instanceof Error ? error.message : 'Unknown error'}`,
        variant: "destructive"
      })
    } finally {
      setLoading(false)
    }
  }

  const downloadPdf = (type: 'comprehensive' | 'budget' | 'expenses') => {
    if (!report) {
      toast({
        title: "No Report",
        description: "Please generate a report first",
        variant: "destructive"
      })
      return
    }

    try {
      console.log('Downloading PDF type:', type)
      console.log('Report data:', report)
      console.log('Budget breakdown:', report.budgetBreakdown)

      const userInfo = { name: 'User', email: '<EMAIL>' } // You can get this from auth context

      switch (type) {
        case 'comprehensive':
          console.log('Generating comprehensive report PDF...')
          PersonalExpensePdfService.generateExpenseReportPdf(report, userInfo)
          break
        case 'budget':
          console.log('Generating budget utilization PDF...')
          if (report.budgetBreakdown.length === 0) {
            toast({
              title: "No Budget Data",
              description: "No budget data available for PDF generation",
              variant: "destructive"
            })
            return
          }
          PersonalExpensePdfService.generateBudgetUtilizationPdf(report.budgetBreakdown, userInfo)
          break
        case 'expenses':
          console.log('Generating expense list PDF...')
          if (expenses.length === 0) {
            toast({
              title: "No Expense Data",
              description: "No expense data available for PDF generation",
              variant: "destructive"
            })
            return
          }
          PersonalExpensePdfService.generateExpenseListPdf(expenses, `Expense List (${startDate} to ${endDate})`, userInfo)
          break
      }

      toast({
        title: "PDF Downloaded",
        description: "Your report has been downloaded successfully"
      })
    } catch (error) {
      console.error('Error downloading PDF:', error)
      toast({
        title: "Download Error",
        description: `Failed to download PDF: ${error instanceof Error ? error.message : 'Unknown error'}`,
        variant: "destructive"
      })
    }
  }

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD'
    }).format(amount)
  }

  const getStatusColor = (isOverBudget: boolean, utilizationPercentage: number) => {
    if (isOverBudget) return 'destructive'
    if (utilizationPercentage > 80) return 'secondary'
    return 'default'
  }

  const getStatusIcon = (isOverBudget: boolean, utilizationPercentage: number) => {
    if (isOverBudget) return <AlertTriangle className="w-4 h-4" />
    if (utilizationPercentage > 80) return <TrendingUp className="w-4 h-4" />
    return <CheckCircle className="w-4 h-4" />
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold">Personal Expense Reports</h2>
          <p className="text-gray-600">Generate detailed reports and insights for your personal expenses</p>
        </div>
        <div className="flex space-x-2">
          <Button onClick={() => downloadPdf('comprehensive')} disabled={!report}>
            <FileText className="w-4 h-4 mr-2" />
            Full Report
          </Button>
          <Button variant="outline" onClick={() => downloadPdf('budget')} disabled={!report}>
            <Target className="w-4 h-4 mr-2" />
            Budget Report
          </Button>
          <Button variant="outline" onClick={() => downloadPdf('expenses')} disabled={!report}>
            <Download className="w-4 h-4 mr-2" />
            Expense List
          </Button>
        </div>
      </div>

      {/* Quick Statistics */}
      {statistics && (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
          <Card>
            <CardContent className="p-6">
              <div className="flex items-center">
                <DollarSign className="h-8 w-8 text-blue-600" />
                <div className="ml-4">
                  <p className="text-sm font-medium text-gray-600">This Month</p>
                  <p className="text-2xl font-bold">{formatCurrency(statistics.thisMonth)}</p>
                </div>
              </div>
            </CardContent>
          </Card>
          
          <Card>
            <CardContent className="p-6">
              <div className="flex items-center">
                <TrendingUp className="h-8 w-8 text-green-600" />
                <div className="ml-4">
                  <p className="text-sm font-medium text-gray-600">Last Month</p>
                  <p className="text-2xl font-bold">{formatCurrency(statistics.lastMonth)}</p>
                </div>
              </div>
            </CardContent>
          </Card>
          
          <Card>
            <CardContent className="p-6">
              <div className="flex items-center">
                <BarChart3 className="h-8 w-8 text-purple-600" />
                <div className="ml-4">
                  <p className="text-sm font-medium text-gray-600">This Year</p>
                  <p className="text-2xl font-bold">{formatCurrency(statistics.thisYear)}</p>
                </div>
              </div>
            </CardContent>
          </Card>
          
          <Card>
            <CardContent className="p-6">
              <div className="flex items-center">
                <Target className="h-8 w-8 text-orange-600" />
                <div className="ml-4">
                  <p className="text-sm font-medium text-gray-600">Budget Usage</p>
                  <p className="text-2xl font-bold">{statistics.budgetUtilization.toFixed(1)}%</p>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      )}

      {/* Report Generation */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center">
            <Filter className="w-5 h-5 mr-2" />
            Generate Report
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-4">
            <div className="space-y-2">
              <Label htmlFor="start-date">Start Date</Label>
              <Input
                id="start-date"
                type="date"
                value={startDate}
                onChange={(e) => setStartDate(e.target.value)}
              />
            </div>
            
            <div className="space-y-2">
              <Label htmlFor="end-date">End Date</Label>
              <Input
                id="end-date"
                type="date"
                value={endDate}
                onChange={(e) => setEndDate(e.target.value)}
              />
            </div>
            
            <div className="space-y-2">
              <Label htmlFor="report-type">Report Type</Label>
              <Select value={reportType} onValueChange={setReportType}>
                <SelectTrigger>
                  <SelectValue placeholder="Select report type" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="comprehensive">Comprehensive</SelectItem>
                  <SelectItem value="budget">Budget Focus</SelectItem>
                  <SelectItem value="category">Category Analysis</SelectItem>
                  <SelectItem value="trend">Trend Analysis</SelectItem>
                </SelectContent>
              </Select>
            </div>
            
            <div className="flex items-end">
              <Button onClick={generateReport} disabled={loading} className="w-full">
                {loading ? 'Generating...' : 'Generate Report'}
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Loading State */}
      {loading && (
        <Card>
          <CardContent className="p-8 text-center">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-4"></div>
            <p className="text-gray-600">Generating your expense report...</p>
          </CardContent>
        </Card>
      )}

      {/* No Data State */}
      {!loading && !report && (
        <Card>
          <CardContent className="p-8 text-center">
            <BarChart3 className="w-12 h-12 text-gray-400 mx-auto mb-4" />
            <h3 className="text-lg font-semibold mb-2">No Report Generated</h3>
            <p className="text-gray-600 mb-4">
              Click "Generate Report" above to create your expense analysis.
            </p>
            <Button onClick={generateReport} disabled={loading}>
              Generate Your First Report
            </Button>
          </CardContent>
        </Card>
      )}

      {/* Report Results */}
      {!loading && report && (
        <Tabs defaultValue="overview" className="space-y-4">
          <TabsList>
            <TabsTrigger value="overview">Overview</TabsTrigger>
            <TabsTrigger value="categories">Categories</TabsTrigger>
            <TabsTrigger value="budgets">Budgets</TabsTrigger>
            <TabsTrigger value="trends">Trends</TabsTrigger>
            <TabsTrigger value="recommendations">Insights</TabsTrigger>
          </TabsList>

          <TabsContent value="overview" className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <Card>
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                  <CardTitle className="text-sm font-medium">Total Expenses</CardTitle>
                  <DollarSign className="h-4 w-4 text-muted-foreground" />
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold">{formatCurrency(report.totalExpenses)}</div>
                  <p className="text-xs text-muted-foreground">
                    {report.topExpenses.length} transactions
                  </p>
                </CardContent>
              </Card>
              
              <Card>
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                  <CardTitle className="text-sm font-medium">Budget Utilization</CardTitle>
                  <Target className="h-4 w-4 text-muted-foreground" />
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold">{report.budgetUtilization.toFixed(1)}%</div>
                  <Progress value={Math.min(report.budgetUtilization, 100)} className="mt-2" />
                </CardContent>
              </Card>
              
              <Card>
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                  <CardTitle className="text-sm font-medium">Savings Rate</CardTitle>
                  <TrendingUp className="h-4 w-4 text-muted-foreground" />
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold">{report.summary.savingsRate.toFixed(1)}%</div>
                  <p className="text-xs text-muted-foreground">
                    {formatCurrency(report.totalBudget - report.totalExpenses)} saved
                  </p>
                </CardContent>
              </Card>
            </div>

            {/* Top Expenses */}
            <Card>
              <CardHeader>
                <CardTitle>Top Expenses</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  {report.topExpenses.slice(0, 5).map((expense, index) => (
                    <div key={expense.id} className="flex items-center justify-between">
                      <div className="flex items-center space-x-3">
                        <div className="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center">
                          <span className="text-sm font-medium text-blue-600">{index + 1}</span>
                        </div>
                        <div>
                          <p className="font-medium">{expense.title}</p>
                          <p className="text-sm text-gray-500">{expense.expense_date}</p>
                        </div>
                      </div>
                      <div className="text-right">
                        <p className="font-medium">{formatCurrency(expense.amount)}</p>
                        <p className="text-sm text-gray-500">{expense.category?.name}</p>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="categories" className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle>Category Breakdown</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {report.categoryBreakdown.map((category) => (
                    <div key={category.category.id} className="space-y-2">
                      <div className="flex items-center justify-between">
                        <div className="flex items-center space-x-3">
                          <div 
                            className="w-4 h-4 rounded-full"
                            style={{ backgroundColor: category.category.color }}
                          />
                          <span className="font-medium">{category.category.name}</span>
                          <Badge variant={getStatusColor(category.isOverBudget, (category.totalAmount / category.budgetAllocated) * 100)}>
                            {category.isOverBudget ? 'Over Budget' : 'On Track'}
                          </Badge>
                        </div>
                        <div className="text-right">
                          <p className="font-medium">{formatCurrency(category.totalAmount)}</p>
                          <p className="text-sm text-gray-500">{category.percentage.toFixed(1)}%</p>
                        </div>
                      </div>
                      <div className="space-y-1">
                        <div className="flex justify-between text-sm text-gray-500">
                          <span>Budget: {formatCurrency(category.budgetAllocated)}</span>
                          <span>Remaining: {formatCurrency(category.budgetRemaining)}</span>
                        </div>
                        <Progress 
                          value={Math.min((category.totalAmount / category.budgetAllocated) * 100, 100)} 
                          className="h-2"
                        />
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="budgets" className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              {report.budgetBreakdown.map((budget) => (
                <Card key={budget.budget.id}>
                  <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                    <CardTitle className="text-base">{budget.budget.name}</CardTitle>
                    {getStatusIcon(budget.isOverBudget, budget.utilizationPercentage)}
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-3">
                      <div className="flex justify-between text-sm">
                        <span>Allocated:</span>
                        <span className="font-medium">{formatCurrency(budget.budget.amount)}</span>
                      </div>
                      <div className="flex justify-between text-sm">
                        <span>Spent:</span>
                        <span className="font-medium">{formatCurrency(budget.totalSpent)}</span>
                      </div>
                      <div className="flex justify-between text-sm">
                        <span>Remaining:</span>
                        <span className={`font-medium ${budget.remainingAmount < 0 ? 'text-red-600' : 'text-green-600'}`}>
                          {formatCurrency(budget.remainingAmount)}
                        </span>
                      </div>
                      <Progress 
                        value={Math.min(budget.utilizationPercentage, 100)} 
                        className="h-3"
                      />
                      <div className="flex justify-between text-xs text-gray-500">
                        <span>{budget.utilizationPercentage.toFixed(1)}% used</span>
                        <span>{budget.daysRemaining} days left</span>
                      </div>
                      {budget.daysRemaining > 0 && (
                        <div className="text-xs text-gray-500">
                          Daily budget remaining: {formatCurrency(budget.dailyBudgetRemaining)}
                        </div>
                      )}
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          </TabsContent>

          <TabsContent value="trends" className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle>Monthly Trends</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {report.monthlyTrend.map((trend, index) => (
                    <div key={index} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                      <div>
                        <p className="font-medium">{trend.month}</p>
                        <p className="text-sm text-gray-500">{trend.expenseCount} expenses</p>
                      </div>
                      <div className="text-right">
                        <p className="font-medium">{formatCurrency(trend.totalExpenses)}</p>
                        <p className="text-sm text-gray-500">
                          {trend.utilizationPercentage.toFixed(1)}% of budget
                        </p>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="recommendations" className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle>Insights & Recommendations</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {report.summary.recommendations.map((recommendation, index) => (
                    <div key={index} className="flex items-start space-x-3 p-3 bg-blue-50 rounded-lg">
                      <div className="w-6 h-6 bg-blue-100 rounded-full flex items-center justify-center mt-0.5">
                        <span className="text-xs font-medium text-blue-600">{index + 1}</span>
                      </div>
                      <p className="text-sm text-blue-800">{recommendation}</p>
                    </div>
                  ))}
                </div>
                
                <div className="mt-6 grid grid-cols-2 gap-4 text-sm">
                  <div>
                    <p className="font-medium text-gray-600">Report Summary</p>
                    <div className="mt-2 space-y-1">
                      <p>Categories: {report.summary.totalCategories}</p>
                      <p>Budgets: {report.summary.totalBudgets}</p>
                      <p>Avg. Expense: {formatCurrency(report.summary.averageExpenseAmount)}</p>
                    </div>
                  </div>
                  <div>
                    <p className="font-medium text-gray-600">Performance</p>
                    <div className="mt-2 space-y-1">
                      <p>Budget Compliance: {report.summary.budgetComplianceRate.toFixed(1)}%</p>
                      <p>Top Category: {report.summary.mostExpensiveCategory}</p>
                      <p>Savings Rate: {report.summary.savingsRate.toFixed(1)}%</p>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      )}
    </div>
  )
}
