
import React, { useState, useEffect } from 'react';
import Layout from '@/components/layout/Layout';
import ErrorBoundary from '@/components/ErrorBoundary';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Switch } from '@/components/ui/switch';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Separator } from '@/components/ui/separator';
import { Settings as SettingsIcon, User, Bell, Shield, Database, Palette, Users } from 'lucide-react';
import UserManagement from '@/components/admin/UserManagement';
import AdminUserManagement from '@/components/admin/AdminUserManagement';
import ClientUserManagement from '@/components/admin/ClientUserManagement';
import SecuritySettings from '@/components/settings/SecuritySettings';
import ProfileSettings from '@/components/settings/ProfileSettings';
import NotificationSettings from '@/components/settings/NotificationSettings';
import SystemSettings from '@/components/settings/SystemSettings';
import SettingsTest from '@/components/settings/SettingsTest';
import DueDateTestPanel from '@/components/admin/DueDateTestPanel';
import DatabaseHealthPanel from '@/components/admin/DatabaseHealthPanel';
import ProjectSiteSync from '@/components/admin/ProjectSiteSync';
import DatabaseSetup from '@/components/admin/DatabaseSetup';
import ExpenseDebug from '@/components/debug/ExpenseDebug';
import { useAuth } from '@/contexts/AuthContext';
import { useRoleAccess } from '@/hooks/useRoleAccess';
import { hasPermission } from '@/lib/permissions';
import { UserService, UserProfile } from '@/lib/userService';

const Settings = () => {
  const { profile } = useAuth();
  const { userRole, permissions } = useRoleAccess();
  const [currentUser, setCurrentUser] = useState<UserProfile | null>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    loadCurrentUser();
  }, []);

  const loadCurrentUser = async () => {
    try {
      const user = await UserService.getCurrentUserProfile();
      setCurrentUser(user);
    } catch (error) {
      console.error('Error loading current user:', error);
    } finally {
      setLoading(false);
    }
  };

  const isAdmin = profile?.role?.role_name === 'admin';

  return (
    <Layout>
      <ErrorBoundary>
        <div className="space-y-6">
        <div className="flex items-center space-x-3">
          <div className="w-12 h-12 bg-gradient-to-r from-blue-600 to-purple-600 rounded-xl flex items-center justify-center">
            <SettingsIcon className="w-6 h-6 text-white" />
          </div>
          <div>
            <h1 className="text-3xl font-bold text-gray-900 font-heading">Settings</h1>
            <p className="text-gray-600">Manage your system preferences and configurations</p>
          </div>
        </div>

        <Tabs defaultValue="security" className="space-y-6">
          <TabsList className={`grid w-full ${isAdmin ? 'grid-cols-5' : 'grid-cols-4'}`}>
            <TabsTrigger value="security" className="flex items-center space-x-2">
              <Shield className="w-4 h-4" />
              <span>Security</span>
            </TabsTrigger>
            <TabsTrigger value="general" className="flex items-center space-x-2">
              <User className="w-4 h-4" />
              <span>General</span>
            </TabsTrigger>
            <TabsTrigger value="notifications" className="flex items-center space-x-2">
              <Bell className="w-4 h-4" />
              <span>Notifications</span>
            </TabsTrigger>
            {(userRole === 'admin' || userRole === 'management') && (
              <TabsTrigger value="system" className="flex items-center space-x-2">
                <Database className="w-4 h-4" />
                <span>System</span>
              </TabsTrigger>
            )}
            {isAdmin && (
              <TabsTrigger value="admin" className="flex items-center space-x-2">
                <Users className="w-4 h-4" />
                <span>Admin</span>
              </TabsTrigger>
            )}
          </TabsList>

          <TabsContent value="general" className="space-y-6">
            <ProfileSettings />
          </TabsContent>

          <TabsContent value="notifications" className="space-y-6">
            <NotificationSettings />

            {/* Due Date Test Panel - Only for admins */}
            {userRole === 'admin' && (
              <div className="mt-8">
                <h3 className="text-lg font-semibold mb-4">Due Date Notification Testing</h3>
                <DueDateTestPanel />
              </div>
            )}
          </TabsContent>

          <TabsContent value="security" className="space-y-6">
            <ErrorBoundary>
              <SecuritySettings />
            </ErrorBoundary>
          </TabsContent>

          <TabsContent value="system" className="space-y-6">
            <SystemSettings />

            {/* Database Health Panel - Only for admins */}
            {userRole === 'admin' && (
              <div className="mt-8">
                <h3 className="text-lg font-semibold mb-4">Database Health & Integrity</h3>
                <DatabaseHealthPanel />
              </div>
            )}

            {/* Project-Site Integration - Only for admins */}
            {userRole === 'admin' && (
              <div className="mt-8">
                <h3 className="text-lg font-semibold mb-4">Project-Site Integration</h3>
                <ProjectSiteSync />
              </div>
            )}

            {/* Database Setup - Only for admins */}
            {userRole === 'admin' && (
              <div className="mt-8">
                <h3 className="text-lg font-semibold mb-4">Database Setup</h3>
                <DatabaseSetup />
              </div>
            )}

            {/* Expense Debug - Only for admins */}
            {userRole === 'admin' && (
              <div className="mt-8">
                <h3 className="text-lg font-semibold mb-4">Expense Debug</h3>
                <ExpenseDebug />
              </div>
            )}
          </TabsContent>

          <TabsContent value="appearance" className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle>Appearance Settings</CardTitle>
                <CardDescription>Customize the look and feel of your system</CardDescription>
              </CardHeader>
              <CardContent className="space-y-6">
                <div className="space-y-2">
                  <Label>Theme</Label>
                  <div className="flex space-x-2">
                    <Button variant="outline" size="sm">Light</Button>
                    <Button variant="outline" size="sm">Dark</Button>
                    <Button variant="outline" size="sm">Auto</Button>
                  </div>
                </div>
                <Separator />
                <div className="space-y-2">
                  <Label>Color Scheme</Label>
                  <div className="flex space-x-2">
                    <div className="w-8 h-8 bg-blue-500 rounded-full border-2 border-blue-600"></div>
                    <div className="w-8 h-8 bg-purple-500 rounded-full"></div>
                    <div className="w-8 h-8 bg-green-500 rounded-full"></div>
                    <div className="w-8 h-8 bg-orange-500 rounded-full"></div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          {/* Admin Panel - Admin Only */}
          {isAdmin && (
            <TabsContent value="admin" className="space-y-6">
              <div className="space-y-6">
                <Card>
                  <CardHeader>
                    <CardTitle>User Management</CardTitle>
                    <CardDescription>Create and manage user accounts</CardDescription>
                  </CardHeader>
                  <CardContent>
                    <AdminUserManagement />
                  </CardContent>
                </Card>

                <Card>
                  <CardHeader>
                    <CardTitle>Client Access Control</CardTitle>
                    <CardDescription>Link client users to their respective clients for project access control</CardDescription>
                  </CardHeader>
                  <CardContent>
                    <ClientUserManagement />
                  </CardContent>
                </Card>
              </div>
            </TabsContent>
          )}
        </Tabs>
        </div>
      </ErrorBoundary>
    </Layout>
  );
};

export default Settings;
