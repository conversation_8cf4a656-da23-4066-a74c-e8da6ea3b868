import React, { useState, useEffect } from 'react'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Textarea } from '@/components/ui/textarea'
import { Badge } from '@/components/ui/badge'
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { Label } from '@/components/ui/label'
import { useToast } from '@/components/ui/use-toast'
import { Progress } from '@/components/ui/progress'
import {
  Plus,
  Search,
  Calendar,
  DollarSign,
  TrendingUp,
  TrendingDown,
  AlertTriangle,
  Edit,
  Trash2,
  <PERSON><PERSON>hart,
  BarChart3,
  Wallet,
  Target,
  Receipt
} from 'lucide-react'
import {
  PersonalExpenseService,
  PersonalExpense,
  PersonalBudget,
  ExpenseCategory,
  ExpenseStats,
  BudgetSummary
} from '@/lib/personalExpenseService'

export default function PersonalExpenses() {
  const [expenses, setExpenses] = useState<PersonalExpense[]>([])
  const [budgets, setBudgets] = useState<PersonalBudget[]>([])
  const [categories, setCategories] = useState<ExpenseCategory[]>([])
  const [stats, setStats] = useState<ExpenseStats | null>(null)
  const [budgetSummaries, setBudgetSummaries] = useState<BudgetSummary[]>([])
  const [loading, setLoading] = useState(true)
  const [searchQuery, setSearchQuery] = useState('')
  const [filterCategory, setFilterCategory] = useState<string>('all')
  const [isExpenseDialogOpen, setIsExpenseDialogOpen] = useState(false)
  const [isBudgetDialogOpen, setIsBudgetDialogOpen] = useState(false)
  const [isCategoryDialogOpen, setIsCategoryDialogOpen] = useState(false)
  const [editingExpense, setEditingExpense] = useState<PersonalExpense | null>(null)
  const [editingBudget, setEditingBudget] = useState<PersonalBudget | null>(null)
  const [editingCategory, setEditingCategory] = useState<ExpenseCategory | null>(null)
  const { toast } = useToast()

  const [expenseFormData, setExpenseFormData] = useState({
    title: '',
    description: '',
    amount: '',
    category_id: '',
    budget_id: '',
    expense_date: new Date().toISOString().split('T')[0],
    payment_method: 'cash'
  })

  const [budgetFormData, setBudgetFormData] = useState({
    name: '',
    category_id: '',
    budget_amount: '',
    period_type: 'monthly' as const,
    start_date: new Date().toISOString().split('T')[0],
    end_date: ''
  })

  const [categoryFormData, setCategoryFormData] = useState({
    name: '',
    description: '',
    color: '#3B82F6',
    icon: '📦'
  })

  useEffect(() => {
    loadData()
  }, [])

  const loadData = async () => {
    try {
      setLoading(true)
      const [expensesData, budgetsData, categoriesData, statsData, summariesData] = await Promise.all([
        PersonalExpenseService.getPersonalExpenses(),
        PersonalExpenseService.getPersonalBudgets(),
        PersonalExpenseService.getExpenseCategories(),
        PersonalExpenseService.getExpenseStats(),
        PersonalExpenseService.getBudgetSummaries()
      ])

      setExpenses(expensesData)
      setBudgets(budgetsData)
      setCategories(categoriesData)
      setStats(statsData)
      setBudgetSummaries(summariesData)
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to load expense data",
        variant: "destructive"
      })
    } finally {
      setLoading(false)
    }
  }

  const handleExpenseSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    try {
      const expenseData = {
        ...expenseFormData,
        amount: parseFloat(expenseFormData.amount),
        budget_id: expenseFormData.budget_id && expenseFormData.budget_id !== "none" ? expenseFormData.budget_id : undefined
      }

      if (editingExpense) {
        await PersonalExpenseService.updatePersonalExpense(editingExpense.id, expenseData)
        toast({
          title: "Success",
          description: "Expense updated successfully"
        })
      } else {
        await PersonalExpenseService.createPersonalExpense(expenseData)
        toast({
          title: "Success",
          description: "Expense created successfully"
        })
      }

      resetExpenseForm()
      setIsExpenseDialogOpen(false)
      loadData()
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to save expense",
        variant: "destructive"
      })
    }
  }

  const handleBudgetSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    try {
      if (editingBudget) {
        await PersonalExpenseService.updatePersonalBudget(editingBudget.id, {
          ...budgetFormData,
          budget_amount: parseFloat(budgetFormData.budget_amount)
        })
        toast({
          title: "Success",
          description: "Budget updated successfully"
        })
      } else {
        await PersonalExpenseService.createPersonalBudget({
          ...budgetFormData,
          budget_amount: parseFloat(budgetFormData.budget_amount)
        })
        toast({
          title: "Success",
          description: "Budget created successfully"
        })
      }

      resetBudgetForm()
      setIsBudgetDialogOpen(false)
      loadData()
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to save budget",
        variant: "destructive"
      })
    }
  }

  const resetExpenseForm = () => {
    setExpenseFormData({
      title: '',
      description: '',
      amount: '',
      category_id: '',
      budget_id: '',
      expense_date: new Date().toISOString().split('T')[0],
      payment_method: 'cash'
    })
    setEditingExpense(null)
  }

  const resetBudgetForm = () => {
    setBudgetFormData({
      name: '',
      category_id: '',
      budget_amount: '',
      period_type: 'monthly',
      start_date: new Date().toISOString().split('T')[0],
      end_date: ''
    })
    setEditingBudget(null)
  }

  const handleCategorySubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    try {
      if (editingCategory) {
        await PersonalExpenseService.updateExpenseCategory(editingCategory.id, categoryFormData)
        toast({
          title: "Success",
          description: "Category updated successfully"
        })
      } else {
        await PersonalExpenseService.createExpenseCategory(categoryFormData)
        toast({
          title: "Success",
          description: "Category created successfully"
        })
      }

      resetCategoryForm()
      setIsCategoryDialogOpen(false)
      await loadData()
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to save category",
        variant: "destructive"
      })
    }
  }

  const resetCategoryForm = () => {
    setCategoryFormData({
      name: '',
      description: '',
      color: '#3B82F6',
      icon: '📦'
    })
    setEditingCategory(null)
  }

  const handleEditCategory = (category: ExpenseCategory) => {
    setCategoryFormData({
      name: category.name,
      description: category.description || '',
      color: category.color,
      icon: category.icon
    })
    setEditingCategory(category)
    setIsCategoryDialogOpen(true)
  }

  const handleDeleteCategory = async (categoryId: string) => {
    if (confirm('Are you sure you want to delete this category? This action cannot be undone.')) {
      try {
        await PersonalExpenseService.deleteExpenseCategory(categoryId)
        toast({
          title: "Success",
          description: "Category deleted successfully"
        })
        await loadData()
      } catch (error) {
        toast({
          title: "Error",
          description: "Failed to delete category",
          variant: "destructive"
        })
      }
    }
  }

  const handleEditExpense = (expense: PersonalExpense) => {
    setExpenseFormData({
      title: expense.title,
      description: expense.description || '',
      amount: expense.amount.toString(),
      category_id: expense.category_id,
      budget_id: expense.budget_id || '',
      expense_date: expense.expense_date,
      payment_method: expense.payment_method
    })
    setEditingExpense(expense)
    setIsExpenseDialogOpen(true)
  }

  const handleEditBudget = (budget: PersonalBudget) => {
    setBudgetFormData({
      name: budget.name,
      category_id: budget.category_id,
      budget_amount: budget.budget_amount.toString(),
      period_type: budget.period_type,
      start_date: budget.start_date,
      end_date: budget.end_date
    })
    setEditingBudget(budget)
    setIsBudgetDialogOpen(true)
  }

  const handleDeleteExpense = async (id: string) => {
    if (!confirm('Are you sure you want to delete this expense?')) return

    try {
      await PersonalExpenseService.deletePersonalExpense(id)
      loadData()
      toast({
        title: "Success",
        description: "Expense deleted successfully"
      })
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to delete expense",
        variant: "destructive"
      })
    }
  }

  const handleDeleteBudget = async (id: string) => {
    if (!confirm('Are you sure you want to delete this budget?')) return

    try {
      await PersonalExpenseService.deletePersonalBudget(id)
      loadData()
      toast({
        title: "Success",
        description: "Budget deleted successfully"
      })
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to delete budget",
        variant: "destructive"
      })
    }
  }

  const filteredExpenses = expenses.filter(expense => {
    const matchesSearch = expense.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         (expense.description?.toLowerCase().includes(searchQuery.toLowerCase()) || false)
    const matchesCategory = filterCategory === 'all' || expense.category_id === filterCategory

    return matchesSearch && matchesCategory
  })

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD'
    }).format(amount)
  }

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString()
  }

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Personal Expenses & Budget</h1>
          <p className="text-gray-600">Track your spending and manage your budget</p>
        </div>

        <div className="flex space-x-2">
          <Dialog open={isCategoryDialogOpen} onOpenChange={setIsCategoryDialogOpen}>
            <DialogTrigger asChild>
              <Button onClick={resetCategoryForm} variant="outline">
                <PieChart className="w-4 h-4 mr-2" />
                Manage Categories
              </Button>
            </DialogTrigger>
            <DialogContent className="max-w-md">
              <DialogHeader>
                <DialogTitle>
                  {editingCategory ? 'Edit Category' : 'Create New Category'}
                </DialogTitle>
              </DialogHeader>
              <form onSubmit={handleCategorySubmit} className="space-y-4">
                <div className="space-y-2">
                  <Label htmlFor="category-name">Category Name *</Label>
                  <Input
                    id="category-name"
                    value={categoryFormData.name}
                    onChange={(e) => setCategoryFormData({...categoryFormData, name: e.target.value})}
                    placeholder="e.g., Food & Dining"
                    required
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="category-description">Description</Label>
                  <Input
                    id="category-description"
                    value={categoryFormData.description}
                    onChange={(e) => setCategoryFormData({...categoryFormData, description: e.target.value})}
                    placeholder="Brief description of this category"
                  />
                </div>

                <div className="grid grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="category-color">Color</Label>
                    <Input
                      id="category-color"
                      type="color"
                      value={categoryFormData.color}
                      onChange={(e) => setCategoryFormData({...categoryFormData, color: e.target.value})}
                    />
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="category-icon">Icon</Label>
                    <Select value={categoryFormData.icon} onValueChange={(value) => setCategoryFormData({...categoryFormData, icon: value})}>
                      <SelectTrigger>
                        <SelectValue placeholder="Select icon" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="🍽️">🍽️ Food</SelectItem>
                        <SelectItem value="🚗">🚗 Transport</SelectItem>
                        <SelectItem value="🎬">🎬 Entertainment</SelectItem>
                        <SelectItem value="🛍️">🛍️ Shopping</SelectItem>
                        <SelectItem value="💡">💡 Bills</SelectItem>
                        <SelectItem value="🏥">🏥 Healthcare</SelectItem>
                        <SelectItem value="📚">📚 Education</SelectItem>
                        <SelectItem value="✈️">✈️ Travel</SelectItem>
                        <SelectItem value="💄">💄 Personal Care</SelectItem>
                        <SelectItem value="🏠">🏠 Home</SelectItem>
                        <SelectItem value="💼">💼 Work</SelectItem>
                        <SelectItem value="🎯">🎯 Goals</SelectItem>
                        <SelectItem value="📦">📦 Other</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                </div>

                <div className="flex justify-end space-x-2">
                  <Button type="button" variant="outline" onClick={() => setIsCategoryDialogOpen(false)}>
                    Cancel
                  </Button>
                  <Button type="submit">
                    {editingCategory ? 'Update Category' : 'Create Category'}
                  </Button>
                </div>
              </form>
            </DialogContent>
          </Dialog>

          <Dialog open={isBudgetDialogOpen} onOpenChange={setIsBudgetDialogOpen}>
            <DialogTrigger asChild>
              <Button onClick={resetBudgetForm} variant="outline">
                <Target className="w-4 h-4 mr-2" />
                Add Budget
              </Button>
            </DialogTrigger>
            <DialogContent className="max-w-md">
              <DialogHeader>
                <DialogTitle>
                  {editingBudget ? 'Edit Budget' : 'Create New Budget'}
                </DialogTitle>
              </DialogHeader>
              <form onSubmit={handleBudgetSubmit} className="space-y-4">
                <div className="space-y-2">
                  <Label htmlFor="budget-name">Budget Name *</Label>
                  <Input
                    id="budget-name"
                    value={budgetFormData.name}
                    onChange={(e) => setBudgetFormData({...budgetFormData, name: e.target.value})}
                    placeholder="e.g., Monthly Food Budget"
                    required
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="budget-category">Category *</Label>
                  <Select value={budgetFormData.category_id} onValueChange={(value) => setBudgetFormData({...budgetFormData, category_id: value})}>
                    <SelectTrigger>
                      <SelectValue placeholder="Select category" />
                    </SelectTrigger>
                    <SelectContent>
                      {categories.map((category) => (
                        <SelectItem key={category.id} value={category.id}>
                          {category.name}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>

                <div className="grid grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="budget-amount">Budget Amount *</Label>
                    <Input
                      id="budget-amount"
                      type="number"
                      step="0.01"
                      value={budgetFormData.budget_amount}
                      onChange={(e) => setBudgetFormData({...budgetFormData, budget_amount: e.target.value})}
                      placeholder="0.00"
                      required
                    />
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="budget-period">Period</Label>
                    <Select value={budgetFormData.period_type} onValueChange={(value) => setBudgetFormData({...budgetFormData, period_type: value as any})}>
                      <SelectTrigger>
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="weekly">Weekly</SelectItem>
                        <SelectItem value="monthly">Monthly</SelectItem>
                        <SelectItem value="quarterly">Quarterly</SelectItem>
                        <SelectItem value="yearly">Yearly</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                </div>

                <div className="grid grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="budget-start">Start Date</Label>
                    <Input
                      id="budget-start"
                      type="date"
                      value={budgetFormData.start_date}
                      onChange={(e) => setBudgetFormData({...budgetFormData, start_date: e.target.value})}
                    />
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="budget-end">End Date</Label>
                    <Input
                      id="budget-end"
                      type="date"
                      value={budgetFormData.end_date}
                      onChange={(e) => setBudgetFormData({...budgetFormData, end_date: e.target.value})}
                    />
                  </div>
                </div>

                <div className="flex space-x-2 pt-4">
                  <Button type="submit" className="flex-1">
                    {editingBudget ? 'Update Budget' : 'Create Budget'}
                  </Button>
                  <Button type="button" variant="outline" onClick={() => setIsBudgetDialogOpen(false)}>
                    Cancel
                  </Button>
                </div>
              </form>
            </DialogContent>
          </Dialog>

          <Dialog open={isExpenseDialogOpen} onOpenChange={setIsExpenseDialogOpen}>
            <DialogTrigger asChild>
              <Button onClick={resetExpenseForm} className="bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700">
                <Plus className="w-4 h-4 mr-2" />
                Add Expense
              </Button>
            </DialogTrigger>
            <DialogContent className="max-w-md">
              <DialogHeader>
                <DialogTitle>
                  {editingExpense ? 'Edit Expense' : 'Add New Expense'}
                </DialogTitle>
              </DialogHeader>
              <form onSubmit={handleExpenseSubmit} className="space-y-4">
                <div className="space-y-2">
                  <Label htmlFor="expense-title">Title *</Label>
                  <Input
                    id="expense-title"
                    value={expenseFormData.title}
                    onChange={(e) => setExpenseFormData({...expenseFormData, title: e.target.value})}
                    placeholder="Enter expense title"
                    required
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="expense-description">Description</Label>
                  <Textarea
                    id="expense-description"
                    value={expenseFormData.description}
                    onChange={(e) => setExpenseFormData({...expenseFormData, description: e.target.value})}
                    placeholder="Enter expense description"
                    rows={2}
                  />
                </div>

                <div className="grid grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="expense-amount">Amount *</Label>
                    <Input
                      id="expense-amount"
                      type="number"
                      step="0.01"
                      value={expenseFormData.amount}
                      onChange={(e) => setExpenseFormData({...expenseFormData, amount: e.target.value})}
                      placeholder="0.00"
                      required
                    />
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="expense-category">Category *</Label>
                    <Select value={expenseFormData.category_id} onValueChange={(value) => setExpenseFormData({...expenseFormData, category_id: value})}>
                      <SelectTrigger>
                        <SelectValue placeholder="Select category" />
                      </SelectTrigger>
                      <SelectContent>
                        {categories.map((category) => (
                          <SelectItem key={category.id} value={category.id}>
                            {category.name}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="expense-budget">Budget (Optional)</Label>
                  <Select value={expenseFormData.budget_id || "none"} onValueChange={(value) => setExpenseFormData({...expenseFormData, budget_id: value === "none" ? "" : value})}>
                    <SelectTrigger>
                      <SelectValue placeholder="Select budget (optional)" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="none">No Budget</SelectItem>
                      {budgets
                        .filter(budget => budget.category_id === expenseFormData.category_id)
                        .map((budget) => (
                          <SelectItem key={budget.id} value={budget.id}>
                            {budget.name} - {formatCurrency(budget.budget_amount)}
                          </SelectItem>
                        ))}
                    </SelectContent>
                  </Select>
                  {expenseFormData.category_id && (
                    <p className="text-xs text-gray-500">
                      {budgets.filter(b => b.category_id === expenseFormData.category_id).length === 0
                        ? `No budgets available for this category (Total budgets: ${budgets.length})`
                        : `${budgets.filter(b => b.category_id === expenseFormData.category_id).length} budget(s) available`
                      }
                    </p>
                  )}
                </div>

                <div className="grid grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="expense-date">Date</Label>
                    <Input
                      id="expense-date"
                      type="date"
                      value={expenseFormData.expense_date}
                      onChange={(e) => setExpenseFormData({...expenseFormData, expense_date: e.target.value})}
                    />
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="expense-payment">Payment Method</Label>
                    <Select value={expenseFormData.payment_method} onValueChange={(value) => setExpenseFormData({...expenseFormData, payment_method: value})}>
                      <SelectTrigger>
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="cash">Cash</SelectItem>
                        <SelectItem value="credit_card">Credit Card</SelectItem>
                        <SelectItem value="debit_card">Debit Card</SelectItem>
                        <SelectItem value="bank_transfer">Bank Transfer</SelectItem>
                        <SelectItem value="mobile_payment">Mobile Payment</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                </div>

                <div className="flex space-x-2 pt-4">
                  <Button type="submit" className="flex-1">
                    {editingExpense ? 'Update Expense' : 'Add Expense'}
                  </Button>
                  <Button type="button" variant="outline" onClick={() => setIsExpenseDialogOpen(false)}>
                    Cancel
                  </Button>
                </div>
              </form>
            </DialogContent>
          </Dialog>
        </div>
      </div>

      {/* Stats Cards */}
      {stats && (
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
          <Card>
            <CardContent className="p-4">
              <div className="flex items-center space-x-2">
                <Wallet className="w-8 h-8 text-blue-600" />
                <div>
                  <div className="text-2xl font-bold text-blue-600">{formatCurrency(stats.total_expenses)}</div>
                  <div className="text-sm text-gray-600">Total Expenses</div>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-4">
              <div className="flex items-center space-x-2">
                <Target className="w-8 h-8 text-green-600" />
                <div>
                  <div className="text-2xl font-bold text-green-600">{formatCurrency(stats.total_budget)}</div>
                  <div className="text-sm text-gray-600">Total Budget</div>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-4">
              <div className="flex items-center space-x-2">
                <TrendingUp className="w-8 h-8 text-orange-600" />
                <div>
                  <div className="text-2xl font-bold text-orange-600">{formatCurrency(stats.expenses_this_month)}</div>
                  <div className="text-sm text-gray-600">This Month</div>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-4">
              <div className="flex items-center space-x-2">
                <AlertTriangle className="w-8 h-8 text-red-600" />
                <div>
                  <div className="text-2xl font-bold text-red-600">{stats.categories_over_budget}</div>
                  <div className="text-sm text-gray-600">Over Budget</div>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      )}

      {/* Budget Progress */}
      {budgetSummaries.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <BarChart3 className="w-5 h-5 mr-2" />
              Budget Overview
            </CardTitle>
          </CardHeader>
          <CardContent>
            {/* Overall Budget Summary */}
            <div className="mb-6 p-4 bg-gray-50 rounded-lg">
              <div className="grid grid-cols-3 gap-4 text-center">
                <div>
                  <div className="text-2xl font-bold text-blue-600">
                    {formatCurrency(budgetSummaries.reduce((sum, s) => sum + s.budget_amount, 0))}
                  </div>
                  <div className="text-sm text-gray-600">Total Budget</div>
                </div>
                <div>
                  <div className="text-2xl font-bold text-orange-600">
                    {formatCurrency(budgetSummaries.reduce((sum, s) => sum + s.spent_amount, 0))}
                  </div>
                  <div className="text-sm text-gray-600">Total Spent</div>
                </div>
                <div>
                  <div className="text-2xl font-bold text-green-600">
                    {formatCurrency(budgetSummaries.reduce((sum, s) => sum + s.remaining_amount, 0))}
                  </div>
                  <div className="text-sm text-gray-600">Remaining</div>
                </div>
              </div>
            </div>

            <div className="space-y-4">
              {budgetSummaries.map((summary, index) => (
                <div key={index} className="space-y-2">
                  <div className="flex justify-between items-center">
                    <span className="font-medium">{summary.category_name}</span>
                    <div className="text-right">
                      <div className="text-sm font-medium">
                        {formatCurrency(summary.spent_amount)} / {formatCurrency(summary.budget_amount)}
                      </div>
                      <div className={`text-xs ${summary.is_over_budget ? 'text-red-600' : 'text-gray-600'}`}>
                        {summary.is_over_budget ? 'Over budget' : `${formatCurrency(summary.remaining_amount)} remaining`}
                      </div>
                    </div>
                  </div>
                  <Progress
                    value={Math.min(summary.percentage_used, 100)}
                    className={`h-2 ${summary.is_over_budget ? 'bg-red-100' : ''}`}
                  />
                  <div className="text-xs text-gray-500">
                    {summary.percentage_used.toFixed(1)}% used
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      )}

      <Tabs defaultValue="expenses" className="space-y-4">
        <TabsList>
          <TabsTrigger value="expenses">Expenses</TabsTrigger>
          <TabsTrigger value="budgets">Budgets</TabsTrigger>
          <TabsTrigger value="categories">Categories</TabsTrigger>
          <TabsTrigger value="reports">Reports</TabsTrigger>
        </TabsList>

        <TabsContent value="expenses" className="space-y-4">
          {/* Filters */}
          <Card>
            <CardContent className="p-4">
              <div className="flex flex-wrap gap-4">
                <div className="flex-1 min-w-64">
                  <div className="relative">
                    <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
                    <Input
                      placeholder="Search expenses..."
                      value={searchQuery}
                      onChange={(e) => setSearchQuery(e.target.value)}
                      className="pl-10"
                    />
                  </div>
                </div>

                <Select value={filterCategory} onValueChange={setFilterCategory}>
                  <SelectTrigger className="w-48">
                    <SelectValue placeholder="Filter by category" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All Categories</SelectItem>
                    {categories.map((category) => (
                      <SelectItem key={category.id} value={category.id}>
                        {category.name}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
            </CardContent>
          </Card>

          {/* Expenses List */}
          <div className="space-y-4">
            {filteredExpenses.length === 0 ? (
              <Card>
                <CardContent className="p-8 text-center">
                  <Receipt className="w-12 h-12 text-gray-400 mx-auto mb-4" />
                  <div className="text-gray-500">
                    {searchQuery || filterCategory !== 'all'
                      ? 'No expenses match your current filters'
                      : 'No expenses yet. Add your first expense to get started!'}
                  </div>
                </CardContent>
              </Card>
            ) : (
              filteredExpenses.map((expense) => (
                <Card key={expense.id} className="transition-all hover:shadow-md">
                  <CardContent className="p-4">
                    <div className="flex items-start justify-between">
                      <div className="flex-1">
                        <div className="flex items-center space-x-3 mb-2">
                          <div
                            className="w-4 h-4 rounded-full"
                            style={{ backgroundColor: expense.category?.color || '#3B82F6' }}
                          ></div>
                          <h3 className="font-semibold">{expense.title}</h3>
                          <Badge variant="outline">{expense.category?.name || 'Unknown'}</Badge>
                        </div>

                        {expense.description && (
                          <p className="text-gray-600 mb-2">{expense.description}</p>
                        )}

                        <div className="flex items-center space-x-4 text-sm text-gray-500">
                          <span className="flex items-center">
                            <Calendar className="w-4 h-4 mr-1" />
                            {formatDate(expense.expense_date)}
                          </span>
                          <span className="flex items-center">
                            <Wallet className="w-4 h-4 mr-1" />
                            {expense.payment_method.replace('_', ' ')}
                          </span>
                          {expense.budget && (
                            <span className="flex items-center">
                              <Target className="w-4 h-4 mr-1" />
                              {expense.budget.name}
                            </span>
                          )}
                        </div>
                      </div>

                      <div className="flex items-center space-x-3">
                        <div className="text-right">
                          <div className="text-lg font-bold text-gray-900">
                            {formatCurrency(expense.amount)}
                          </div>
                        </div>

                        <div className="flex items-center space-x-2">
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => handleEditExpense(expense)}
                          >
                            <Edit className="w-4 h-4" />
                          </Button>

                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => handleDeleteExpense(expense.id)}
                            className="text-red-600 hover:text-red-700"
                          >
                            <Trash2 className="w-4 h-4" />
                          </Button>
                        </div>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              ))
            )}
          </div>
        </TabsContent>

        <TabsContent value="budgets" className="space-y-4">
          {/* Budgets List */}
          <div className="space-y-4">
            {budgets.length === 0 ? (
              <Card>
                <CardContent className="p-8 text-center">
                  <Target className="w-12 h-12 text-gray-400 mx-auto mb-4" />
                  <div className="text-gray-500">
                    No budgets yet. Create your first budget to start tracking your spending goals!
                  </div>
                </CardContent>
              </Card>
            ) : (
              budgets.map((budget) => {
                const summary = budgetSummaries.find(s => s.category_name === budget.category?.name)
                return (
                  <Card key={budget.id} className="transition-all hover:shadow-md">
                    <CardContent className="p-4">
                      <div className="flex items-start justify-between">
                        <div className="flex-1">
                          <div className="flex items-center space-x-3 mb-2">
                            <div
                              className="w-4 h-4 rounded-full"
                              style={{ backgroundColor: budget.category?.color || '#3B82F6' }}
                            ></div>
                            <h3 className="font-semibold">{budget.name}</h3>
                            <Badge variant="outline">{budget.category?.name || 'Unknown'}</Badge>
                            <Badge className="bg-blue-100 text-blue-800">
                              {budget.period_type}
                            </Badge>
                          </div>

                          <div className="flex items-center space-x-4 text-sm text-gray-500 mb-3">
                            <span className="flex items-center">
                              <Calendar className="w-4 h-4 mr-1" />
                              {formatDate(budget.start_date)} - {formatDate(budget.end_date)}
                            </span>
                          </div>

                          {summary && (
                            <div className="space-y-2">
                              <div className="flex justify-between text-sm">
                                <span>Spent: {formatCurrency(summary.spent_amount)}</span>
                                <span>Budget: {formatCurrency(summary.budget_amount)}</span>
                              </div>
                              <Progress
                                value={Math.min(summary.percentage_used, 100)}
                                className={`h-2 ${summary.is_over_budget ? 'bg-red-100' : ''}`}
                              />
                              <div className={`text-xs ${summary.is_over_budget ? 'text-red-600' : 'text-gray-500'}`}>
                                {summary.percentage_used.toFixed(1)}% used
                                {summary.is_over_budget && ' (Over budget!)'}
                              </div>
                            </div>
                          )}
                        </div>

                        <div className="flex items-center space-x-3">
                          <div className="text-right">
                            <div className="text-lg font-bold text-gray-900">
                              {formatCurrency(budget.budget_amount)}
                            </div>
                          </div>

                          <div className="flex items-center space-x-2">
                            <Button
                              variant="ghost"
                              size="sm"
                              onClick={() => handleEditBudget(budget)}
                            >
                              <Edit className="w-4 h-4" />
                            </Button>

                            <Button
                              variant="ghost"
                              size="sm"
                              onClick={() => handleDeleteBudget(budget.id)}
                              className="text-red-600 hover:text-red-700"
                            >
                              <Trash2 className="w-4 h-4" />
                            </Button>
                          </div>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                )
              })
            )}
          </div>
        </TabsContent>

        <TabsContent value="categories" className="space-y-4">
          {/* Categories List */}
          <div className="space-y-4">
            {categories.length === 0 ? (
              <Card>
                <CardContent className="p-8 text-center">
                  <PieChart className="w-12 h-12 text-gray-400 mx-auto mb-4" />
                  <div className="text-gray-500">
                    No categories yet. Create your first category to start organizing your expenses!
                  </div>
                </CardContent>
              </Card>
            ) : (
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                {categories.map((category) => (
                  <Card key={category.id} className="transition-all hover:shadow-md">
                    <CardContent className="p-4">
                      <div className="flex items-start justify-between">
                        <div className="flex-1">
                          <div className="flex items-center space-x-3 mb-2">
                            <div
                              className="w-6 h-6 rounded-full flex items-center justify-center text-white text-sm"
                              style={{ backgroundColor: category.color }}
                            >
                              {category.icon}
                            </div>
                            <h3 className="font-semibold">{category.name}</h3>
                          </div>

                          {category.description && (
                            <p className="text-gray-600 text-sm mb-3">{category.description}</p>
                          )}

                          <div className="flex items-center space-x-2 text-xs text-gray-500">
                            <span>Created {formatDate(category.created_at)}</span>
                          </div>
                        </div>

                        <div className="flex space-x-1">
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => handleEditCategory(category)}
                          >
                            <Edit className="w-4 h-4" />
                          </Button>
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => handleDeleteCategory(category.id)}
                            className="text-red-600 hover:text-red-700"
                          >
                            <Trash2 className="w-4 h-4" />
                          </Button>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                ))}
              </div>
            )}
          </div>
        </TabsContent>

        <TabsContent value="reports" className="space-y-4">
          <Card>
            <CardContent className="p-8 text-center">
              <BarChart3 className="w-16 h-16 text-blue-500 mx-auto mb-4" />
              <h3 className="text-xl font-semibold mb-2">Personal Expense Reports</h3>
              <p className="text-gray-600 mb-6">
                Generate detailed reports and insights for your personal expenses and budget usage.
              </p>
              <Button
                onClick={() => {
                  const newWindow = window.open('/personal/reports', '_blank')
                  if (!newWindow) {
                    // Fallback if popup blocked
                    window.location.href = '/personal/reports'
                  }
                }}
                className="bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700"
              >
                <Receipt className="w-4 h-4 mr-2" />
                Open Reports Dashboard
              </Button>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  )
}