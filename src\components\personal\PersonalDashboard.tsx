import React, { useState, useEffect } from 'react'
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Progress } from '@/components/ui/progress'
import {
  CheckCircle2,
  Circle,
  Clock,
  AlertTriangle,
  DollarSign,
  Target,
  TrendingUp,
  Calendar,
  Plus,
  ArrowRight,
  FileText,
  BarChart3
} from 'lucide-react'
import { PersonalTodoService, TodoStats, PersonalTodo } from '@/lib/personalTodoService'
import { PersonalExpenseService, ExpenseStats, BudgetSummary, PersonalExpense } from '@/lib/personalExpenseService'
import { useNavigate } from 'react-router-dom'

export default function PersonalDashboard() {
  const [todoStats, setTodoStats] = useState<TodoStats | null>(null)
  const [expenseStats, setExpenseStats] = useState<ExpenseStats | null>(null)
  const [budgetSummaries, setBudgetSummaries] = useState<BudgetSummary[]>([])
  const [recentTodos, setRecentTodos] = useState<PersonalTodo[]>([])
  const [recentExpenses, setRecentExpenses] = useState<PersonalExpense[]>([])
  const [loading, setLoading] = useState(true)
  const navigate = useNavigate()

  useEffect(() => {
    loadDashboardData()
  }, [])

  const loadDashboardData = async () => {
    try {
      setLoading(true)
      const [
        todoStatsData,
        expenseStatsData,
        budgetSummariesData,
        todosData,
        expensesData
      ] = await Promise.all([
        PersonalTodoService.getTodoStats(),
        PersonalExpenseService.getExpenseStats(),
        PersonalExpenseService.getBudgetSummaries(),
        PersonalTodoService.getUserTodos(),
        PersonalExpenseService.getPersonalExpenses()
      ])

      setTodoStats(todoStatsData)
      setExpenseStats(expenseStatsData)
      setBudgetSummaries(budgetSummariesData)
      setRecentTodos(todosData.slice(0, 5)) // Get 5 most recent todos
      setRecentExpenses(expensesData.slice(0, 5)) // Get 5 most recent expenses
    } catch (error) {
      console.error('Error loading dashboard data:', error)
    } finally {
      setLoading(false)
    }
  }

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD'
    }).format(amount)
  }

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString()
  }

  const isOverdue = (dueDate: string, status: string) => {
    if (status === 'completed') return false
    return new Date(dueDate) < new Date()
  }

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Personal Dashboard</h1>
          <p className="text-gray-600">Your personal productivity and expense overview</p>
        </div>
      </div>

      {/* Quick Stats */}
      <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
        {todoStats && (
          <>
            <Card>
              <CardContent className="p-4">
                <div className="flex items-center space-x-2">
                  <CheckCircle2 className="w-8 h-8 text-green-600" />
                  <div>
                    <div className="text-2xl font-bold text-green-600">{todoStats.completed}</div>
                    <div className="text-sm text-gray-600">Completed Tasks</div>
                  </div>
                </div>
              </CardContent>
            </Card>
            
            <Card>
              <CardContent className="p-4">
                <div className="flex items-center space-x-2">
                  <Clock className="w-8 h-8 text-blue-600" />
                  <div>
                    <div className="text-2xl font-bold text-blue-600">{todoStats.pending + todoStats.in_progress}</div>
                    <div className="text-sm text-gray-600">Active Tasks</div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </>
        )}

        {expenseStats && (
          <>
            <Card>
              <CardContent className="p-4">
                <div className="flex items-center space-x-2">
                  <DollarSign className="w-8 h-8 text-purple-600" />
                  <div>
                    <div className="text-2xl font-bold text-purple-600">{formatCurrency(expenseStats.expenses_this_month)}</div>
                    <div className="text-sm text-gray-600">This Month</div>
                  </div>
                </div>
              </CardContent>
            </Card>
            
            <Card>
              <CardContent className="p-4">
                <div className="flex items-center space-x-2">
                  <Target className="w-8 h-8 text-orange-600" />
                  <div>
                    <div className="text-2xl font-bold text-orange-600">{expenseStats.categories_over_budget}</div>
                    <div className="text-sm text-gray-600">Over Budget</div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </>
        )}
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Todo Overview */}
        <Card>
          <CardHeader className="flex flex-row items-center justify-between">
            <CardTitle className="flex items-center">
              <CheckCircle2 className="w-5 h-5 mr-2" />
              Recent Tasks
            </CardTitle>
            <Button
              variant="outline"
              size="sm"
              onClick={() => navigate('/personal/todos')}
            >
              View All
              <ArrowRight className="w-4 h-4 ml-1" />
            </Button>
          </CardHeader>
          <CardContent>
            {todoStats && (
              <div className="mb-4">
                <div className="flex justify-between text-sm mb-2">
                  <span>Progress</span>
                  <span>{todoStats.completed} of {todoStats.total} completed</span>
                </div>
                <Progress 
                  value={todoStats.total > 0 ? (todoStats.completed / todoStats.total) * 100 : 0} 
                  className="h-2"
                />
              </div>
            )}

            <div className="space-y-3">
              {recentTodos.length === 0 ? (
                <div className="text-center py-4 text-gray-500">
                  No tasks yet. Create your first task to get started!
                </div>
              ) : (
                recentTodos.map((todo) => (
                  <div key={todo.id} className="flex items-center space-x-3 p-2 rounded-lg hover:bg-gray-50">
                    {todo.status === 'completed' ? (
                      <CheckCircle2 className="w-5 h-5 text-green-600" />
                    ) : (
                      <Circle className="w-5 h-5 text-gray-400" />
                    )}
                    
                    <div className="flex-1">
                      <div className={`font-medium ${todo.status === 'completed' ? 'line-through text-gray-500' : ''}`}>
                        {todo.title}
                      </div>
                      <div className="flex items-center space-x-2 text-xs text-gray-500">
                        <Badge className={`text-xs ${
                          todo.priority === 'urgent' ? 'bg-red-100 text-red-800' :
                          todo.priority === 'high' ? 'bg-orange-100 text-orange-800' :
                          todo.priority === 'medium' ? 'bg-yellow-100 text-yellow-800' :
                          'bg-green-100 text-green-800'
                        }`}>
                          {todo.priority}
                        </Badge>
                        {todo.due_date && (
                          <span className={`flex items-center ${isOverdue(todo.due_date, todo.status) ? 'text-red-600' : ''}`}>
                            <Calendar className="w-3 h-3 mr-1" />
                            {formatDate(todo.due_date)}
                            {isOverdue(todo.due_date, todo.status) && (
                              <AlertTriangle className="w-3 h-3 ml-1" />
                            )}
                          </span>
                        )}
                      </div>
                    </div>
                  </div>
                ))
              )}
            </div>

            {todoStats && todoStats.overdue > 0 && (
              <div className="mt-4 p-3 bg-red-50 border border-red-200 rounded-lg">
                <div className="flex items-center text-red-800">
                  <AlertTriangle className="w-4 h-4 mr-2" />
                  <span className="font-medium">{todoStats.overdue} overdue tasks</span>
                </div>
              </div>
            )}
          </CardContent>
        </Card>

        {/* Budget Overview */}
        <Card>
          <CardHeader className="flex flex-row items-center justify-between">
            <CardTitle className="flex items-center">
              <Target className="w-5 h-5 mr-2" />
              Budget Status
            </CardTitle>
            <Button
              variant="outline"
              size="sm"
              onClick={() => navigate('/personal/expenses')}
            >
              View All
              <ArrowRight className="w-4 h-4 ml-1" />
            </Button>
          </CardHeader>
          <CardContent>
            {budgetSummaries.length === 0 ? (
              <div className="text-center py-4 text-gray-500">
                No budgets set up yet. Create your first budget to start tracking!
              </div>
            ) : (
              <div className="space-y-4">
                {budgetSummaries.slice(0, 4).map((summary, index) => (
                  <div key={index} className="space-y-2">
                    <div className="flex justify-between items-center">
                      <span className="font-medium text-sm">{summary.category_name}</span>
                      <div className="text-right">
                        <div className="text-sm font-medium">
                          {formatCurrency(summary.spent_amount)} / {formatCurrency(summary.budget_amount)}
                        </div>
                      </div>
                    </div>
                    <Progress 
                      value={Math.min(summary.percentage_used, 100)} 
                      className={`h-2 ${summary.is_over_budget ? 'bg-red-100' : ''}`}
                    />
                    <div className={`text-xs ${summary.is_over_budget ? 'text-red-600' : 'text-gray-500'}`}>
                      {summary.percentage_used.toFixed(1)}% used
                      {summary.is_over_budget && ' (Over budget!)'}
                    </div>
                  </div>
                ))}
              </div>
            )}

            {expenseStats && expenseStats.categories_over_budget > 0 && (
              <div className="mt-4 p-3 bg-red-50 border border-red-200 rounded-lg">
                <div className="flex items-center text-red-800">
                  <AlertTriangle className="w-4 h-4 mr-2" />
                  <span className="font-medium">{expenseStats.categories_over_budget} categories over budget</span>
                </div>
              </div>
            )}
          </CardContent>
        </Card>
      </div>

      {/* Recent Expenses */}
      <Card>
        <CardHeader className="flex flex-row items-center justify-between">
          <CardTitle className="flex items-center">
            <DollarSign className="w-5 h-5 mr-2" />
            Recent Expenses
          </CardTitle>
          <div className="flex space-x-2">
            <Button
              variant="outline"
              size="sm"
              onClick={() => navigate('/personal/reports')}
            >
              <FileText className="w-4 h-4 mr-1" />
              Reports
            </Button>
            <Button
              variant="outline"
              size="sm"
              onClick={() => navigate('/personal/expenses')}
            >
              View All
              <ArrowRight className="w-4 h-4 ml-1" />
            </Button>
          </div>
        </CardHeader>
        <CardContent>
          {recentExpenses.length === 0 ? (
            <div className="text-center py-4 text-gray-500">
              No expenses recorded yet. Add your first expense to start tracking!
            </div>
          ) : (
            <div className="space-y-3">
              {recentExpenses.map((expense) => (
                <div key={expense.id} className="flex items-center justify-between p-3 rounded-lg hover:bg-gray-50">
                  <div className="flex items-center space-x-3">
                    <div 
                      className="w-3 h-3 rounded-full" 
                      style={{ backgroundColor: expense.category?.color || '#3B82F6' }}
                    ></div>
                    <div>
                      <div className="font-medium">{expense.title}</div>
                      <div className="text-sm text-gray-500">
                        {expense.category?.name} • {formatDate(expense.expense_date)}
                      </div>
                    </div>
                  </div>
                  <div className="text-lg font-bold text-gray-900">
                    {formatCurrency(expense.amount)}
                  </div>
                </div>
              ))}
            </div>
          )}
        </CardContent>
      </Card>

      {/* Quick Actions */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <Card className="cursor-pointer hover:shadow-md transition-shadow" onClick={() => navigate('/personal/todos')}>
          <CardContent className="p-6 text-center">
            <Plus className="w-12 h-12 text-blue-600 mx-auto mb-4" />
            <h3 className="font-semibold text-lg mb-2">Add New Task</h3>
            <p className="text-gray-600">Create a new todo item to stay organized</p>
          </CardContent>
        </Card>

        <Card className="cursor-pointer hover:shadow-md transition-shadow" onClick={() => navigate('/personal/expenses')}>
          <CardContent className="p-6 text-center">
            <DollarSign className="w-12 h-12 text-green-600 mx-auto mb-4" />
            <h3 className="font-semibold text-lg mb-2">Add New Expense</h3>
            <p className="text-gray-600">Record a new expense and track your spending</p>
          </CardContent>
        </Card>

        <Card className="cursor-pointer hover:shadow-md transition-shadow" onClick={() => navigate('/personal/reports')}>
          <CardContent className="p-6 text-center">
            <BarChart3 className="w-12 h-12 text-purple-600 mx-auto mb-4" />
            <h3 className="font-semibold text-lg mb-2">View Reports</h3>
            <p className="text-gray-600">Generate detailed expense reports and insights</p>
          </CardContent>
        </Card>
      </div>
    </div>
  )
}
