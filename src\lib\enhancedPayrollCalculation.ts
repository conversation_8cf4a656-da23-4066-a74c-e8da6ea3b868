import { supabase } from './supabase';

export interface EmployeePerformanceData {
  employee_id: string;
  employee_name: string;
  position: string;
  department: string;
  contract_daily_rate: number;
  days_worked: number;
  total_hours: number;
  expected_hours: number;
  performance_ratio: number;
  performance_status: 'under_performing' | 'meeting_expectations' | 'over_achieving';
  gross_pay: number;
  attendance_rate: number;
  efficiency_score: number;
}

export interface PayrollCalculationResult {
  employee_id: string;
  user_id?: string;
  employee_name: string;
  days_worked: number;
  daily_rate: number;
  gross_pay: number;
  regular_hours: number;
  overtime_hours: number;
  performance_data: EmployeePerformanceData;
  deductions: {
    tax: number;
    insurance: number;
    other: number;
    total: number;
  };
  net_pay: number;
}

export class EnhancedPayrollCalculation {
  /**
   * Calculate payroll based on daily rates and performance tracking
   */
  static async calculatePayrollForPeriod(
    periodStart: string,
    periodEnd: string
  ): Promise<PayrollCalculationResult[]> {
    try {
      console.log('🔄 Calculating enhanced payroll for period:', periodStart, 'to', periodEnd);

      // Get all active employee contracts (both workforce and system users)
      const { data: contracts, error: contractsError } = await supabase
        .from('employee_contracts')
        .select(`
          *,
          workforce:workforce!employee_contracts_employee_id_fkey(
            id, name, position, department, employee_id
          ),
          user_profile:user_profiles!employee_contracts_user_id_fkey(
            user_id, first_name, last_name, email
          )
        `)
        .eq('is_active', true);

      if (contractsError) throw contractsError;

      // Get time entries for the period
      const { data: timeEntries, error: timeError } = await supabase
        .from('time_entries')
        .select('*')
        .gte('clock_in_time', periodStart)
        .lte('clock_in_time', periodEnd)
        .eq('status', 'completed');

      if (timeError) throw timeError;

      console.log(`📊 Found ${contracts?.length || 0} active contracts and ${timeEntries?.length || 0} completed time entries`);

      const results: PayrollCalculationResult[] = [];

      for (const contract of contracts || []) {
        // Skip if neither workforce nor user profile exists
        if (!contract.workforce && !contract.user_profile) continue;

        // Determine employee details first
        const employeeName = contract.workforce?.name ||
                           (contract.user_profile ? `${contract.user_profile.first_name} ${contract.user_profile.last_name}` : 'Unknown');

        // Get employee's time entries (check both employee_id and user_id)
        const employeeTimeEntries = timeEntries?.filter(
          entry => entry.employee_id === contract.employee_id || entry.user_id === contract.user_id
        ) || [];

        console.log(`👤 Processing ${employeeName}: ${employeeTimeEntries.length} time entries found`);

        // Calculate days worked and hours
        const daysWorked = this.calculateDaysWorked(employeeTimeEntries);
        const totalHours = this.calculateTotalHours(employeeTimeEntries);
        const { regularHours, overtimeHours } = this.calculateRegularAndOvertimeHours(
          totalHours, 
          contract.overtime_threshold_hours || 8,
          daysWorked
        );

        // Calculate performance metrics
        const performanceData = this.calculatePerformanceMetrics(
          contract,
          daysWorked,
          totalHours,
          employeeTimeEntries
        );

        // Calculate gross pay based on daily rate
        const grossPay = daysWorked * contract.daily_rate;

        // Calculate deductions (simplified)
        const deductions = this.calculateDeductions(grossPay);

        // Calculate net pay
        const netPay = grossPay - deductions.total;

        // Determine employee details
        const employeeId = contract.employee_id || contract.user_id;

        results.push({
          employee_id: employeeId,
          user_id: contract.user_id,
          employee_name: employeeName,
          days_worked: daysWorked,
          daily_rate: contract.daily_rate,
          gross_pay: grossPay,
          regular_hours: regularHours,
          overtime_hours: overtimeHours,
          performance_data: performanceData,
          deductions,
          net_pay: netPay
        });
      }

      console.log('✅ Enhanced payroll calculation completed for', results.length, 'employees');
      return results;

    } catch (error) {
      console.error('❌ Error calculating enhanced payroll:', error);
      throw error;
    }
  }

  /**
   * Calculate unique days worked from time entries
   */
  private static calculateDaysWorked(timeEntries: any[]): number {
    const uniqueDays = new Set(
      timeEntries.map(entry => 
        new Date(entry.clock_in_time).toDateString()
      )
    );
    return uniqueDays.size;
  }

  /**
   * Calculate total hours worked
   */
  private static calculateTotalHours(timeEntries: any[]): number {
    return timeEntries.reduce((total, entry) => {
      if (entry.clock_out_time) {
        const clockIn = new Date(entry.clock_in_time);
        const clockOut = new Date(entry.clock_out_time);
        const hours = (clockOut.getTime() - clockIn.getTime()) / (1000 * 60 * 60);
        const breakHours = (entry.break_duration_minutes || 0) / 60;
        return total + Math.max(0, hours - breakHours);
      }
      return total;
    }, 0);
  }

  /**
   * Calculate regular and overtime hours
   */
  private static calculateRegularAndOvertimeHours(
    totalHours: number,
    dailyThreshold: number,
    daysWorked: number
  ): { regularHours: number; overtimeHours: number } {
    const expectedRegularHours = daysWorked * dailyThreshold;
    
    if (totalHours <= expectedRegularHours) {
      return {
        regularHours: totalHours,
        overtimeHours: 0
      };
    } else {
      return {
        regularHours: expectedRegularHours,
        overtimeHours: totalHours - expectedRegularHours
      };
    }
  }

  /**
   * Calculate performance metrics
   */
  private static calculatePerformanceMetrics(
    contract: any,
    daysWorked: number,
    totalHours: number,
    timeEntries: any[]
  ): EmployeePerformanceData {
    const expectedHoursPerDay = contract.overtime_threshold_hours || 8;
    const expectedTotalHours = daysWorked * expectedHoursPerDay;
    
    // Performance ratio (actual hours vs expected hours)
    const performanceRatio = expectedTotalHours > 0 ? totalHours / expectedTotalHours : 0;
    
    // Determine performance status
    let performanceStatus: 'under_performing' | 'meeting_expectations' | 'over_achieving';
    if (performanceRatio < 0.85) {
      performanceStatus = 'under_performing';
    } else if (performanceRatio > 1.15) {
      performanceStatus = 'over_achieving';
    } else {
      performanceStatus = 'meeting_expectations';
    }

    // Calculate attendance rate (days worked vs expected work days in period)
    const workDaysInPeriod = this.calculateWorkDaysInPeriod();
    const attendanceRate = workDaysInPeriod > 0 ? (daysWorked / workDaysInPeriod) * 100 : 0;

    // Calculate efficiency score (combination of attendance and performance)
    const efficiencyScore = (attendanceRate * 0.4) + (performanceRatio * 100 * 0.6);

    return {
      employee_id: contract.employee_id,
      employee_name: contract.workforce.name,
      position: contract.workforce.position,
      department: contract.workforce.department,
      contract_daily_rate: contract.daily_rate,
      days_worked: daysWorked,
      total_hours: totalHours,
      expected_hours: expectedTotalHours,
      performance_ratio: performanceRatio,
      performance_status: performanceStatus,
      gross_pay: daysWorked * contract.daily_rate,
      attendance_rate: Math.min(100, attendanceRate),
      efficiency_score: Math.min(100, efficiencyScore)
    };
  }

  /**
   * Calculate deductions (simplified)
   */
  private static calculateDeductions(grossPay: number) {
    const tax = grossPay * 0.15; // 15% tax
    const insurance = grossPay * 0.05; // 5% insurance
    const other = grossPay * 0.02; // 2% other deductions
    
    return {
      tax,
      insurance,
      other,
      total: tax + insurance + other
    };
  }

  /**
   * Calculate work days in period (simplified - assumes 5 work days per week)
   */
  private static calculateWorkDaysInPeriod(): number {
    // This is a simplified calculation
    // In a real system, you'd calculate actual work days excluding weekends and holidays
    return 10; // Assuming 2-week pay period with 10 work days
  }

  /**
   * Get performance insights for management
   */
  static async getPerformanceInsights(
    periodStart: string,
    periodEnd: string
  ): Promise<{
    underPerformers: EmployeePerformanceData[];
    overAchievers: EmployeePerformanceData[];
    averageEfficiency: number;
    totalProductiveHours: number;
  }> {
    try {
      const payrollResults = await this.calculatePayrollForPeriod(periodStart, periodEnd);
      
      const performanceData = payrollResults.map(result => result.performance_data);
      
      const underPerformers = performanceData.filter(
        data => data.performance_status === 'under_performing'
      );
      
      const overAchievers = performanceData.filter(
        data => data.performance_status === 'over_achieving'
      );
      
      const averageEfficiency = performanceData.reduce(
        (sum, data) => sum + data.efficiency_score, 0
      ) / performanceData.length;
      
      const totalProductiveHours = performanceData.reduce(
        (sum, data) => sum + data.total_hours, 0
      );

      return {
        underPerformers,
        overAchievers,
        averageEfficiency,
        totalProductiveHours
      };

    } catch (error) {
      console.error('❌ Error getting performance insights:', error);
      throw error;
    }
  }
}

export default EnhancedPayrollCalculation;
