import { supabase } from './supabase'

export interface ExpenseCategory {
  id: string
  user_id: string
  name: string
  description?: string
  color: string
  icon: string
  is_active: boolean
  created_at: string
  updated_at: string
}

export interface PersonalBudget {
  id: string
  user_id: string
  category_id: string
  name: string
  budget_amount: number
  period_type: 'weekly' | 'monthly' | 'quarterly' | 'yearly'
  start_date: string
  end_date: string
  is_active: boolean
  created_at: string
  updated_at: string
  category?: ExpenseCategory
}

export interface PersonalExpense {
  id: string
  user_id: string
  category_id: string
  budget_id?: string
  title: string
  description?: string
  amount: number
  expense_date: string
  payment_method: string
  receipt_url?: string
  tags?: string[]
  is_recurring: boolean
  recurring_frequency?: string
  created_at: string
  updated_at: string
  category?: ExpenseCategory
  budget?: PersonalBudget
}

export interface ExpenseStats {
  total_expenses: number
  total_budget: number
  budget_used_percentage: number
  expenses_this_month: number
  budget_this_month: number
  categories_over_budget: number
  largest_expense: number
  average_expense: number
}

export interface BudgetSummary {
  category_name: string
  budget_amount: number
  spent_amount: number
  remaining_amount: number
  percentage_used: number
  is_over_budget: boolean
  color: string
}

export class PersonalExpenseService {
  // Expense Categories
  static async getExpenseCategories(): Promise<ExpenseCategory[]> {
    try {
      // Get current user
      const { data: { user } } = await supabase.auth.getUser()
      if (!user) throw new Error('User not authenticated')

      const { data, error } = await supabase
        .from('expense_categories')
        .select('*')
        .eq('user_id', user.id)
        .eq('is_active', true)
        .order('name')

      if (error) throw error

      // If user has no categories, create default ones
      if (!data || data.length === 0) {
        console.log('No expense categories found for user, creating defaults...')
        await this.createDefaultCategories()

        // Fetch again after creating defaults
        const { data: newData, error: newError } = await supabase
          .from('expense_categories')
          .select('*')
          .eq('user_id', user.id)
          .eq('is_active', true)
          .order('name')

        if (newError) throw newError
        return newData || []
      }

      return data || []
    } catch (error) {
      console.error('Error fetching expense categories:', error)
      throw error
    }
  }

  // Create default expense categories for new users
  static async createDefaultCategories(): Promise<void> {
    try {
      const { data: { user } } = await supabase.auth.getUser()
      if (!user) throw new Error('User not authenticated')

      const defaultCategories = [
        {
          name: 'Food & Dining',
          description: 'Restaurants, groceries, and food expenses',
          color: '#FF6B6B',
          icon: '🍽️',
          is_active: true,
          user_id: user.id
        },
        {
          name: 'Transportation',
          description: 'Gas, public transport, car maintenance',
          color: '#4ECDC4',
          icon: '🚗',
          is_active: true,
          user_id: user.id
        },
        {
          name: 'Entertainment',
          description: 'Movies, games, hobbies, and fun activities',
          color: '#45B7D1',
          icon: '🎬',
          is_active: true,
          user_id: user.id
        },
        {
          name: 'Shopping',
          description: 'Clothing, electronics, and general shopping',
          color: '#96CEB4',
          icon: '🛍️',
          is_active: true,
          user_id: user.id
        },
        {
          name: 'Bills & Utilities',
          description: 'Electricity, water, internet, phone bills',
          color: '#FFEAA7',
          icon: '💡',
          is_active: true,
          user_id: user.id
        },
        {
          name: 'Healthcare',
          description: 'Medical expenses, pharmacy, insurance',
          color: '#FD79A8',
          icon: '🏥',
          is_active: true,
          user_id: user.id
        },
        {
          name: 'Education',
          description: 'Books, courses, training, and learning',
          color: '#A29BFE',
          icon: '📚',
          is_active: true,
          user_id: user.id
        },
        {
          name: 'Travel',
          description: 'Vacation, business trips, and travel expenses',
          color: '#FD79A8',
          icon: '✈️',
          is_active: true,
          user_id: user.id
        },
        {
          name: 'Personal Care',
          description: 'Haircuts, cosmetics, personal hygiene',
          color: '#FDCB6E',
          icon: '💄',
          is_active: true,
          user_id: user.id
        },
        {
          name: 'Other',
          description: 'Miscellaneous expenses',
          color: '#6C5CE7',
          icon: '📦',
          is_active: true,
          user_id: user.id
        }
      ]

      const { error } = await supabase
        .from('expense_categories')
        .insert(defaultCategories)

      if (error) throw error
      console.log('Default expense categories created successfully')
    } catch (error) {
      console.error('Error creating default categories:', error)
      throw error
    }
  }

  static async createExpenseCategory(categoryData: Omit<ExpenseCategory, 'id' | 'user_id' | 'created_at' | 'updated_at'>): Promise<ExpenseCategory> {
    try {
      // Get current user
      const { data: { user } } = await supabase.auth.getUser()
      if (!user) throw new Error('User not authenticated')

      const { data, error } = await supabase
        .from('expense_categories')
        .insert([{
          ...categoryData,
          user_id: user.id
        }])
        .select()
        .single()

      if (error) throw error
      return data
    } catch (error) {
      console.error('Error creating expense category:', error)
      throw error
    }
  }

  static async updateExpenseCategory(categoryId: string, categoryData: Partial<Omit<ExpenseCategory, 'id' | 'user_id' | 'created_at' | 'updated_at'>>): Promise<ExpenseCategory> {
    try {
      const { data, error } = await supabase
        .from('expense_categories')
        .update({
          ...categoryData,
          updated_at: new Date().toISOString()
        })
        .eq('id', categoryId)
        .select()
        .single()

      if (error) throw error
      return data
    } catch (error) {
      console.error('Error updating expense category:', error)
      throw error
    }
  }

  static async deleteExpenseCategory(categoryId: string): Promise<void> {
    try {
      // Soft delete by setting is_active to false
      const { error } = await supabase
        .from('expense_categories')
        .update({
          is_active: false,
          updated_at: new Date().toISOString()
        })
        .eq('id', categoryId)

      if (error) throw error
    } catch (error) {
      console.error('Error deleting expense category:', error)
      throw error
    }
  }

  // Personal Budgets
  static async getPersonalBudgets(): Promise<PersonalBudget[]> {
    try {
      const { data: budgetsData, error } = await supabase
        .from('personal_budgets')
        .select('*')
        .eq('is_active', true)
        .order('created_at', { ascending: false })

      if (error) throw error

      // Get categories separately
      const categoryIds = [...new Set(budgetsData?.map(b => b.category_id) || [])]
      let categories: ExpenseCategory[] = []

      if (categoryIds.length > 0) {
        const { data: categoriesData } = await supabase
          .from('expense_categories')
          .select('*')
          .in('id', categoryIds)
        categories = categoriesData || []
      }

      // Merge the data
      const enrichedBudgets = budgetsData?.map(budget => ({
        ...budget,
        category: categories.find(c => c.id === budget.category_id)
      })) || []

      return enrichedBudgets
    } catch (error) {
      console.error('Error fetching personal budgets:', error)
      throw error
    }
  }

  static async createPersonalBudget(budgetData: Omit<PersonalBudget, 'id' | 'user_id' | 'created_at' | 'updated_at'>): Promise<PersonalBudget> {
    try {
      // Get current user
      const { data: { user } } = await supabase.auth.getUser()
      if (!user) throw new Error('User not authenticated')

      const { data, error } = await supabase
        .from('personal_budgets')
        .insert([{
          ...budgetData,
          user_id: user.id,
          is_active: true
        }])
        .select()
        .single()

      if (error) throw error
      return data
    } catch (error) {
      console.error('Error creating personal budget:', error)
      throw error
    }
  }

  static async updatePersonalBudget(id: string, updates: Partial<PersonalBudget>): Promise<PersonalBudget> {
    try {
      const { data, error } = await supabase
        .from('personal_budgets')
        .update({
          ...updates,
          updated_at: new Date().toISOString()
        })
        .eq('id', id)
        .select()
        .single()

      if (error) throw error
      return data
    } catch (error) {
      console.error('Error updating personal budget:', error)
      throw error
    }
  }

  static async deletePersonalBudget(id: string): Promise<void> {
    try {
      const { error } = await supabase
        .from('personal_budgets')
        .delete()
        .eq('id', id)

      if (error) throw error
    } catch (error) {
      console.error('Error deleting personal budget:', error)
      throw error
    }
  }

  // Personal Expenses
  static async getPersonalExpenses(): Promise<PersonalExpense[]> {
    try {
      const { data: expensesData, error } = await supabase
        .from('personal_expenses')
        .select('*')
        .order('expense_date', { ascending: false })

      if (error) throw error

      // Get categories and budgets separately
      const categoryIds = [...new Set(expensesData?.map(e => e.category_id) || [])]
      const budgetIds = [...new Set(expensesData?.filter(e => e.budget_id).map(e => e.budget_id) || [])]

      let categories: ExpenseCategory[] = []
      let budgets: PersonalBudget[] = []

      if (categoryIds.length > 0) {
        const { data: categoriesData } = await supabase
          .from('expense_categories')
          .select('*')
          .in('id', categoryIds)
        categories = categoriesData || []
      }

      if (budgetIds.length > 0) {
        const { data: budgetsData } = await supabase
          .from('personal_budgets')
          .select('*')
          .in('id', budgetIds)
        budgets = budgetsData || []
      }

      // Merge the data
      const enrichedExpenses = expensesData?.map(expense => ({
        ...expense,
        category: categories.find(c => c.id === expense.category_id),
        budget: expense.budget_id ? budgets.find(b => b.id === expense.budget_id) : undefined
      })) || []

      return enrichedExpenses
    } catch (error) {
      console.error('Error fetching personal expenses:', error)
      throw error
    }
  }

  static async createPersonalExpense(expenseData: Omit<PersonalExpense, 'id' | 'user_id' | 'created_at' | 'updated_at'>): Promise<PersonalExpense> {
    try {
      // Get current user
      const { data: { user } } = await supabase.auth.getUser()
      if (!user) throw new Error('User not authenticated')

      const { data, error } = await supabase
        .from('personal_expenses')
        .insert([{
          ...expenseData,
          user_id: user.id
        }])
        .select()
        .single()

      if (error) throw error
      return data
    } catch (error) {
      console.error('Error creating personal expense:', error)
      throw error
    }
  }

  static async updatePersonalExpense(id: string, updates: Partial<PersonalExpense>): Promise<PersonalExpense> {
    try {
      const { data, error } = await supabase
        .from('personal_expenses')
        .update({
          ...updates,
          updated_at: new Date().toISOString()
        })
        .eq('id', id)
        .select()
        .single()

      if (error) throw error
      return data
    } catch (error) {
      console.error('Error updating personal expense:', error)
      throw error
    }
  }

  static async deletePersonalExpense(id: string): Promise<void> {
    try {
      const { error } = await supabase
        .from('personal_expenses')
        .delete()
        .eq('id', id)

      if (error) throw error
    } catch (error) {
      console.error('Error deleting personal expense:', error)
      throw error
    }
  }

  // Analytics and Statistics
  static async getExpenseStats(): Promise<ExpenseStats> {
    try {
      const [expenses, budgets] = await Promise.all([
        this.getPersonalExpenses(),
        this.getPersonalBudgets()
      ])

      const currentMonth = new Date().getMonth()
      const currentYear = new Date().getFullYear()

      const expensesThisMonth = expenses.filter(expense => {
        const expenseDate = new Date(expense.expense_date)
        return expenseDate.getMonth() === currentMonth && expenseDate.getFullYear() === currentYear
      })

      const totalExpenses = expenses.reduce((sum, expense) => sum + expense.amount, 0)
      const totalBudget = budgets.reduce((sum, budget) => sum + budget.budget_amount, 0)
      const expensesThisMonthAmount = expensesThisMonth.reduce((sum, expense) => sum + expense.amount, 0)

      // Calculate budget-specific spending
      const budgetLinkedExpenses = expenses.filter(expense => expense.budget_id)
      const totalBudgetSpent = budgetLinkedExpenses.reduce((sum, expense) => sum + expense.amount, 0)

      const budgetSummaries = await this.getBudgetSummaries()
      const categoriesOverBudget = budgetSummaries.filter(summary => summary.is_over_budget).length

      const expenseAmounts = expenses.map(e => e.amount)
      const largestExpense = expenseAmounts.length > 0 ? Math.max(...expenseAmounts) : 0
      const averageExpense = expenseAmounts.length > 0 ? totalExpenses / expenseAmounts.length : 0

      return {
        total_expenses: totalExpenses,
        total_budget: totalBudget,
        budget_used_percentage: totalBudget > 0 ? (totalBudgetSpent / totalBudget) * 100 : 0,
        expenses_this_month: expensesThisMonthAmount,
        budget_this_month: totalBudget, // Simplified - could be more sophisticated
        categories_over_budget: categoriesOverBudget,
        largest_expense: largestExpense,
        average_expense: averageExpense
      }
    } catch (error) {
      console.error('Error getting expense stats:', error)
      throw error
    }
  }

  static async getBudgetSummaries(): Promise<BudgetSummary[]> {
    try {
      const [budgets, expenses] = await Promise.all([
        this.getPersonalBudgets(),
        this.getPersonalExpenses()
      ])

      return budgets.map(budget => {
        // Get expenses specifically linked to this budget
        const budgetExpenses = expenses.filter(expense => expense.budget_id === budget.id)
        const spentAmount = budgetExpenses.reduce((sum, expense) => sum + expense.amount, 0)
        const remainingAmount = budget.budget_amount - spentAmount
        const percentageUsed = budget.budget_amount > 0 ? (spentAmount / budget.budget_amount) * 100 : 0
        const isOverBudget = spentAmount > budget.budget_amount

        return {
          category_name: budget.category?.name || 'Unknown',
          budget_amount: budget.budget_amount,
          spent_amount: spentAmount,
          remaining_amount: remainingAmount,
          percentage_used: percentageUsed,
          is_over_budget: isOverBudget,
          color: budget.category?.color || '#3B82F6'
        }
      })
    } catch (error) {
      console.error('Error getting budget summaries:', error)
      throw error
    }
  }

  // Search and Filter
  static async searchExpenses(query: string): Promise<PersonalExpense[]> {
    try {
      const { data: expensesData, error } = await supabase
        .from('personal_expenses')
        .select('*')
        .or(`title.ilike.%${query}%,description.ilike.%${query}%`)
        .order('expense_date', { ascending: false })

      if (error) throw error

      // Get categories separately
      const categoryIds = [...new Set(expensesData?.map(e => e.category_id) || [])]
      let categories: ExpenseCategory[] = []

      if (categoryIds.length > 0) {
        const { data: categoriesData } = await supabase
          .from('expense_categories')
          .select('*')
          .in('id', categoryIds)
        categories = categoriesData || []
      }

      // Merge the data
      const enrichedExpenses = expensesData?.map(expense => ({
        ...expense,
        category: categories.find(c => c.id === expense.category_id)
      })) || []

      return enrichedExpenses
    } catch (error) {
      console.error('Error searching expenses:', error)
      throw error
    }
  }

  static async getExpensesByCategory(categoryId: string): Promise<PersonalExpense[]> {
    try {
      const { data: expensesData, error } = await supabase
        .from('personal_expenses')
        .select('*')
        .eq('category_id', categoryId)
        .order('expense_date', { ascending: false })

      if (error) throw error

      // Get category data
      const { data: categoryData } = await supabase
        .from('expense_categories')
        .select('*')
        .eq('id', categoryId)
        .single()

      // Merge the data
      const enrichedExpenses = expensesData?.map(expense => ({
        ...expense,
        category: categoryData
      })) || []

      return enrichedExpenses
    } catch (error) {
      console.error('Error fetching expenses by category:', error)
      throw error
    }
  }

  static async getExpensesByDateRange(startDate: string, endDate: string): Promise<PersonalExpense[]> {
    try {
      const { data: expensesData, error } = await supabase
        .from('personal_expenses')
        .select('*')
        .gte('expense_date', startDate)
        .lte('expense_date', endDate)
        .order('expense_date', { ascending: false })

      if (error) throw error

      // Get categories separately
      const categoryIds = [...new Set(expensesData?.map(e => e.category_id) || [])]
      let categories: ExpenseCategory[] = []

      if (categoryIds.length > 0) {
        const { data: categoriesData } = await supabase
          .from('expense_categories')
          .select('*')
          .in('id', categoryIds)
        categories = categoriesData || []
      }

      // Merge the data
      const enrichedExpenses = expensesData?.map(expense => ({
        ...expense,
        category: categories.find(c => c.id === expense.category_id)
      })) || []

      return enrichedExpenses
    } catch (error) {
      console.error('Error fetching expenses by date range:', error)
      throw error
    }
  }
}
