import { supabase } from './supabase';
import { FinancialService } from './financials';
import { EnhancedPayrollCalculation, PayrollCalculationResult } from './enhancedPayrollCalculation';

export interface EmployeeContract {
  id: string;
  employee_id: string;
  user_id?: string;
  contract_type: 'full_time' | 'part_time' | 'contractor' | 'temporary';
  base_salary?: number;
  daily_rate: number;
  hourly_rate: number;
  overtime_rate: number;
  start_date: string;
  end_date?: string;
  is_active: boolean;
  benefits_eligible: boolean;
  vacation_days_per_year: number;
  sick_days_per_year: number;
  payment_frequency: 'daily' | 'weekly' | 'monthly';
  overtime_threshold_hours: number;
  created_at: string;
  updated_at: string;
  workforce?: {
    id: string;
    name: string;
    position: string;
    department: string;
    employee_id?: string;
  };
  user_profile?: {
    user_id: string;
    first_name: string;
    last_name: string;
    employee_id?: string;
  };
  notes?: string;
}

export interface PayrollPeriod {
  id: string;
  period_start: string;
  period_end: string;
  pay_date: string;
  status: 'draft' | 'calculated' | 'approved' | 'paid';
  total_gross_pay: number;
  total_net_pay: number;
  total_deductions: number;
  created_by?: string;
  updated_by?: string;
  created_at: string;
  updated_at: string;
  expense_entry_id?: string;
  created_by_profile?: {
    first_name: string;
    last_name: string;
  };
}

export interface PayrollEntry {
  id: string;
  payroll_period_id: string;
  employee_id: string;
  user_id?: string;
  days_worked: number;
  regular_hours: number;
  overtime_hours: number;
  daily_rate: number;
  hourly_rate: number;
  overtime_rate: number;
  gross_pay: number;
  tax_deductions: number;
  other_deductions: number;
  net_pay: number;
  bonus: number;
  commission: number;
  status: 'draft' | 'calculated' | 'approved' | 'paid';
  created_at: string;
  updated_at: string;
  workforce?: {
    id: string;
    name: string;
    position: string;
    department: string;
    employee_id?: string;
  };
}

export interface TimeEntryWithPay {
  id: string;
  user_id?: string;
  employee_id?: string;
  site_id: string;
  project_id?: string;
  clock_in_time: string;
  clock_out_time?: string;
  break_duration_minutes: number;
  total_hours: number;
  overtime_hours: number;
  days_worked: number;
  daily_rate: number;
  hourly_rate: number;
  overtime_rate: number;
  total_pay: number;
  status: 'active' | 'completed' | 'approved' | 'paid';
  work_description?: string;
  location_lat?: number;
  location_lng?: number;
  logged_by?: string;
  created_at: string;
  updated_at: string;
}

export class PayrollService {
  // Employee Contract Management with Workforce Integration
  static async createEmployeeContract(contractData: Omit<EmployeeContract, 'id' | 'created_at' | 'updated_at'>): Promise<EmployeeContract> {
    try {
      // Create contract in employee_contracts table
      const { data: contractRecord, error: contractError } = await supabase
        .from('employee_contracts')
        .insert([{
          employee_id: contractData.employee_id,
          user_id: contractData.user_id,
          contract_type: contractData.contract_type,
          base_salary: contractData.base_salary,
          daily_rate: contractData.daily_rate,
          hourly_rate: contractData.hourly_rate,
          overtime_rate: contractData.overtime_rate,
          start_date: contractData.start_date,
          end_date: contractData.end_date,
          is_active: contractData.is_active,
          benefits_eligible: contractData.benefits_eligible,
          vacation_days_per_year: contractData.vacation_days_per_year,
          sick_days_per_year: contractData.sick_days_per_year,
          payment_frequency: contractData.payment_frequency,
          overtime_threshold_hours: contractData.overtime_threshold_hours
        }])
        .select('*')
        .single();

      if (contractError) throw contractError;

      // Also update user_profiles if user_id is provided
      if (contractData.user_id) {
        await supabase
          .from('user_profiles')
          .update({
            daily_rate: contractData.daily_rate,
            hourly_rate: contractData.hourly_rate,
            salary: contractData.base_salary
          })
          .eq('user_id', contractData.user_id);
      }

      // Fetch and merge workforce/user profile data for the created contract
      let enrichedContract = contractRecord;

      if (contractRecord.employee_id) {
        try {
          const { data: workforceData } = await supabase
            .from('workforce')
            .select('id, name, position, department, employee_id')
            .eq('id', contractRecord.employee_id)
            .single();

          enrichedContract = {
            ...contractRecord,
            workforce: workforceData
          };
        } catch (workforceError) {
          console.warn('Could not fetch workforce data for created contract:', workforceError);
        }
      }

      if (contractRecord.user_id) {
        try {
          const { data: profileData } = await supabase
            .from('user_profiles')
            .select('user_id, first_name, last_name, employee_id')
            .eq('user_id', contractRecord.user_id)
            .single();

          enrichedContract = {
            ...enrichedContract,
            user_profile: profileData
          };
        } catch (profileError) {
          console.warn('Could not fetch user profile data for created contract:', profileError);
        }
      }

      console.log('✅ Employee contract created successfully with enriched data');
      return enrichedContract;
    } catch (error) {
      console.error('Error creating employee contract:', error);
      throw error;
    }
  }

  static async getEmployeeContractByEmployeeId(employeeId: string): Promise<EmployeeContract | null> {
    try {
      const { data: contractData, error: contractError } = await supabase
        .from('employee_contracts')
        .select('*')
        .eq('employee_id', employeeId)
        .eq('is_active', true)
        .order('created_at', { ascending: false })
        .limit(1)
        .single();

      if (contractError && contractError.code !== 'PGRST116') {
        throw contractError;
      }

      return contractData || null;
    } catch (error) {
      console.error('Error fetching employee contract by employee_id:', error);
      return null;
    }
  }

  static async getEmployeeContractByUserId(userId: string): Promise<EmployeeContract | null> {
    try {
      // Try to get from employee_contracts table first
      const { data: contractData, error: contractError } = await supabase
        .from('employee_contracts')
        .select('*')
        .eq('user_id', userId)
        .eq('is_active', true)
        .order('created_at', { ascending: false })
        .limit(1)
        .single();

      if (contractData) {
        return contractData;
      }

      // Fallback: get from user_profiles table
      const { data, error } = await supabase
        .from('user_profiles')
        .select('*')
        .eq('user_id', userId)
        .single();

      if (error && error.code !== 'PGRST116') throw error;

      if (!data) return null;

      // Only return contract if user profile has actual rate data
      if (!data.daily_rate && !data.hourly_rate) {
        console.warn(`⚠️ User profile for ${userId} has no rate data, no contract available`);
        return null;
      }

      // Return contract based on user profile with actual rates
      return {
        id: data.id,
        user_id: userId,
        contract_type: 'full_time',
        base_salary: data.salary || undefined,
        daily_rate: data.daily_rate || (data.hourly_rate ? data.hourly_rate * 8 : null),
        hourly_rate: data.hourly_rate || (data.daily_rate ? data.daily_rate / 8 : null),
        overtime_rate: data.hourly_rate ? data.hourly_rate * 1.5 : (data.daily_rate ? (data.daily_rate / 8) * 1.5 : null),
        start_date: data.hire_date || new Date().toISOString().split('T')[0],
        end_date: undefined,
        is_active: true,
        benefits_eligible: true,
        vacation_days_per_year: 15,
        sick_days_per_year: 10,
        payment_frequency: 'daily',
        overtime_threshold_hours: 8.0,
        created_at: data.created_at || new Date().toISOString(),
        updated_at: data.updated_at || new Date().toISOString()
      };
    } catch (error) {
      console.error('Error fetching employee contract:', error);
      return null;
    }
  }

  static async updateEmployeeContract(id: string, updates: Partial<EmployeeContract>): Promise<EmployeeContract> {
    try {
      console.log('🔄 Updating employee contract:', { id, updates });

      const { data, error } = await supabase
        .from('employee_contracts')
        .update({
          contract_type: updates.contract_type,
          daily_rate: updates.daily_rate,
          hourly_rate: updates.hourly_rate,
          overtime_rate: updates.overtime_rate,
          base_salary: updates.base_salary,
          payment_frequency: updates.payment_frequency,
          overtime_threshold_hours: updates.overtime_threshold_hours,
          start_date: updates.start_date,
          end_date: updates.end_date,
          is_active: updates.is_active,
          benefits_eligible: updates.benefits_eligible,
          vacation_days_per_year: updates.vacation_days_per_year,
          sick_days_per_year: updates.sick_days_per_year,
          updated_at: new Date().toISOString()
        })
        .eq('id', id)
        .select()
        .single();

      if (error) {
        console.error('❌ Error updating employee contract:', error);
        throw error;
      }

      // Fetch and merge workforce/user profile data for the updated contract
      let enrichedContract = data;

      if (data.employee_id) {
        try {
          const { data: workforceData } = await supabase
            .from('workforce')
            .select('id, name, position, department, employee_id')
            .eq('id', data.employee_id)
            .single();

          enrichedContract = {
            ...data,
            workforce: workforceData
          };
        } catch (workforceError) {
          console.warn('Could not fetch workforce data for updated contract:', workforceError);
        }
      }

      if (data.user_id) {
        try {
          const { data: profileData } = await supabase
            .from('user_profiles')
            .select('user_id, first_name, last_name, employee_id')
            .eq('user_id', data.user_id)
            .single();

          enrichedContract = {
            ...enrichedContract,
            user_profile: profileData
          };
        } catch (profileError) {
          console.warn('Could not fetch user profile data for updated contract:', profileError);
        }
      }

      console.log('✅ Employee contract updated successfully with enriched data');
      return enrichedContract;
    } catch (error) {
      console.error('❌ Error updating employee contract:', error);
      throw error;
    }
  }

  static async getAllEmployeeContracts(): Promise<EmployeeContract[]> {
    try {
      console.log('🔄 Fetching all employee contracts...');

      // Get employee contracts
      const { data: contractsData, error: contractsError } = await supabase
        .from('employee_contracts')
        .select('*')
        .eq('is_active', true)
        .order('created_at', { ascending: false });

      if (contractsError) {
        console.error('❌ Error fetching employee contracts:', contractsError);
        throw contractsError;
      }

      // Get related data separately
      const employeeIds = [...new Set(contractsData?.filter(c => c.employee_id).map(c => c.employee_id) || [])];
      const userIds = [...new Set(contractsData?.filter(c => c.user_id).map(c => c.user_id) || [])];

      let workforceEmployees: any[] = [];
      let userProfiles: any[] = [];

      // Get workforce data
      if (employeeIds.length > 0) {
        try {
          const { data: workforceData } = await supabase
            .from('workforce')
            .select('id, name, position, department, employee_id, email, user_id')
            .in('id', employeeIds);
          workforceEmployees = workforceData || [];
        } catch (workforceError) {
          console.warn('Could not fetch workforce data:', workforceError);
        }
      }

      // Get user profiles
      if (userIds.length > 0) {
        try {
          const { data: profilesData } = await supabase
            .from('user_profiles')
            .select('user_id, first_name, last_name, email, employee_id')
            .in('user_id', userIds);
          userProfiles = profilesData || [];
        } catch (profileError) {
          console.warn('Could not fetch user profiles:', profileError);
        }
      }

      // Merge the data
      const enrichedContracts = contractsData?.map(contract => ({
        ...contract,
        workforce: workforceEmployees.find(w => w.id === contract.employee_id),
        user_profile: userProfiles.find(u => u.user_id === contract.user_id)
      })) || [];

      console.log(`✅ Fetched ${enrichedContracts.length} employee contracts with linked data`);
      return enrichedContracts;


    } catch (error) {
      console.error('❌ Error fetching employee contracts:', error);
      return [];
    }
  }

  // Workforce Integration - Get employee contract data
  static async getEmployeeContract(employeeId?: string, userId?: string): Promise<any> {
    try {
      console.log('🔄 Fetching employee contract data...');

      let contractData = null;

      // Try to get contract by employee_id first
      if (employeeId) {
        const { data, error } = await supabase
          .from('employee_contracts')
          .select('*')
          .eq('employee_id', employeeId)
          .eq('is_active', true)
          .order('created_at', { ascending: false })
          .limit(1)
          .single();

        if (!error && data) {
          // Get workforce data separately
          try {
            const { data: workforceData } = await supabase
              .from('workforce')
              .select('id, name, position, department, employee_id, email, user_id')
              .eq('id', employeeId)
              .single();

            contractData = {
              ...data,
              workforce: workforceData
            };
          } catch (workforceError) {
            console.warn('Could not fetch workforce data:', workforceError);
            contractData = data;
          }
        }
      }

      // If no contract found by employee_id, try user_id
      if (!contractData && userId) {
        const { data, error } = await supabase
          .from('employee_contracts')
          .select('*')
          .eq('user_id', userId)
          .eq('is_active', true)
          .order('created_at', { ascending: false })
          .limit(1)
          .single();

        if (!error && data) {
          // Get user profile data separately
          try {
            const { data: profileData } = await supabase
              .from('user_profiles')
              .select('user_id, first_name, last_name, email, employee_id')
              .eq('user_id', userId)
              .single();

            contractData = {
              ...data,
              user_profile: profileData
            };
          } catch (profileError) {
            console.warn('Could not fetch user profile data:', profileError);
            contractData = data;
          }
        }
      }

      // If still no contract, return null to indicate no contract found
      // This prevents using hardcoded fallback rates that override actual contract rates

      console.log('✅ Employee contract data retrieved:', contractData ? 'Found' : 'Using defaults');
      return contractData;
    } catch (error) {
      console.error('❌ Error fetching employee contract:', error);
      return null;
    }
  }

  // Enhanced Time Entry Processing with Daily Rate Calculation
  static async processTimeEntryWithPay(timeEntryId: string): Promise<TimeEntryWithPay> {
    try {
      // Get the time entry
      const { data: timeEntry, error: timeError } = await supabase
        .from('time_entries')
        .select('*')
        .eq('id', timeEntryId)
        .single();

      if (timeError) throw timeError;

      // Get employee contract for rates
      // For workforce employees, use employee_id; for system users, use user_id
      const contract = timeEntry.employee_id
        ? await this.getEmployeeContractByEmployeeId(timeEntry.employee_id)
        : await this.getEmployeeContractByUserId(timeEntry.user_id);

      if (!contract) {
        throw new Error('No active contract found for employee');
      }

      // Calculate pay based on daily rate system
      const totalHours = timeEntry.total_hours || 0;
      const overtimeThreshold = contract.overtime_threshold_hours;
      const overtimeHours = Math.max(0, totalHours - overtimeThreshold);
      const regularHours = totalHours - overtimeHours;

      let totalPay = 0;
      let daysWorked = 0;

      if (contract.payment_frequency === 'daily') {
        // Daily rate calculation
        daysWorked = totalHours > 0 ? 1 : 0; // Full day if any hours worked
        const dailyPay = daysWorked * contract.daily_rate;
        const overtimePay = overtimeHours * contract.overtime_rate;
        totalPay = dailyPay + overtimePay;
      } else {
        // Hourly rate calculation (fallback)
        const regularPay = regularHours * contract.hourly_rate;
        const overtimePay = overtimeHours * contract.overtime_rate;
        totalPay = regularPay + overtimePay;
        daysWorked = totalHours / 8; // Estimate days based on 8-hour workday
      }

      // Update time entry with pay information
      const { data: updatedEntry, error: updateError } = await supabase
        .from('time_entries')
        .update({
          daily_rate: contract.daily_rate,
          hourly_rate: contract.hourly_rate,
          overtime_rate: contract.overtime_rate,
          overtime_hours: overtimeHours,
          days_worked: daysWorked,
          total_pay: totalPay,
          updated_at: new Date().toISOString()
        })
        .eq('id', timeEntryId)
        .select()
        .single();

      if (updateError) throw updateError;
      return updatedEntry;
    } catch (error) {
      console.error('Error processing time entry with pay:', error);
      throw error;
    }
  }

  // Real Payroll Period Management with Database Integration
  static async createPayrollPeriod(periodData: {
    period_start: string;
    period_end: string;
    pay_date: string;
    created_by?: string;
  }): Promise<PayrollPeriod> {
    try {
      console.log('🔄 Creating payroll period in database:', periodData);

      // Get current user if not provided
      let createdBy = periodData.created_by;
      if (!createdBy) {
        const { data: { user } } = await supabase.auth.getUser();
        createdBy = user?.id;
      }

      const { data, error } = await supabase
        .from('payroll_periods')
        .insert([{
          period_start: periodData.period_start,
          period_end: periodData.period_end,
          pay_date: periodData.pay_date,
          status: 'draft',
          total_gross_pay: 0,
          total_net_pay: 0,
          total_deductions: 0,
          created_by: createdBy,
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString()
        }])
        .select()
        .single();

      if (error) {
        console.error('❌ Database error creating payroll period:', error);
        throw error;
      }

      console.log('✅ Payroll period created successfully:', data);
      return data;
    } catch (error) {
      console.error('❌ Error creating payroll period:', error);
      throw error;
    }
  }

  static async getPayrollPeriods(): Promise<PayrollPeriod[]> {
    try {
      console.log('🔄 Fetching payroll periods from database...');

      const { data: periodsData, error } = await supabase
        .from('payroll_periods')
        .select('*')
        .order('period_start', { ascending: false });

      if (error) {
        console.error('❌ Database error fetching payroll periods:', error);
        throw error;
      }

      // Get created_by user information separately if needed
      const createdByUserIds = [...new Set(periodsData?.filter(p => p.created_by).map(p => p.created_by) || [])];
      let createdByUsers: any[] = [];

      if (createdByUserIds.length > 0) {
        try {
          const { data: usersData } = await supabase
            .from('user_profiles')
            .select('user_id, first_name, last_name')
            .in('user_id', createdByUserIds);
          createdByUsers = usersData || [];
        } catch (userError) {
          console.warn('Could not fetch created_by user data:', userError);
        }
      }

      // Merge the data
      const enrichedPeriods = periodsData?.map(period => ({
        ...period,
        created_by_profile: createdByUsers.find(u => u.user_id === period.created_by)
      })) || [];

      console.log(`✅ Fetched ${enrichedPeriods.length} payroll periods from database`);
      return enrichedPeriods;
    } catch (error) {
      console.error('❌ Error fetching payroll periods:', error);
      return [];
    }
  }

  static async getPayrollPeriod(id: string): Promise<PayrollPeriod | null> {
    try {
      const periods = await this.getPayrollPeriods();
      return periods.find(p => p.id === id) || null;
    } catch (error) {
      console.error('Error fetching payroll period:', error);
      return null;
    }
  }

  static async updatePayrollPeriodStatus(id: string, status: PayrollPeriod['status']): Promise<PayrollPeriod> {
    try {
      const { data, error } = await supabase
        .from('payroll_periods')
        .update({ status, updated_at: new Date().toISOString() })
        .eq('id', id)
        .select()
        .single();

      if (error) throw error;

      // If marking as paid, create expense entries
      if (status === 'paid') {
        await this.createPayrollExpenseEntries(id);
      }

      return data;
    } catch (error) {
      console.error('Error updating payroll period status:', error);
      throw error;
    }
  }

  // Real Payroll Entry Management with Database Integration
  static async getPayrollEntries(payrollPeriodId: string): Promise<PayrollEntry[]> {
    try {
      console.log(`🔄 Fetching payroll entries for period ${payrollPeriodId}...`);

      const { data: entriesData, error } = await supabase
        .from('payroll_entries')
        .select('*')
        .eq('payroll_period_id', payrollPeriodId)
        .order('created_at', { ascending: false });

      if (error) {
        console.error('❌ Database error fetching payroll entries:', error);
        throw error;
      }

      // Get user profiles and workforce data separately
      const userIds = [...new Set(entriesData?.filter(e => e.user_id).map(e => e.user_id) || [])];
      const employeeIds = [...new Set(entriesData?.filter(e => e.employee_id).map(e => e.employee_id) || [])];

      let userProfiles: any[] = [];
      let workforceEmployees: any[] = [];

      // Get user profiles
      if (userIds.length > 0) {
        try {
          const { data: profilesData } = await supabase
            .from('user_profiles')
            .select('user_id, first_name, last_name, employee_id')
            .in('user_id', userIds);
          userProfiles = profilesData || [];
        } catch (profileError) {
          console.warn('Could not fetch user profiles:', profileError);
        }
      }

      // Get workforce employees
      if (employeeIds.length > 0) {
        try {
          const { data: workforceData } = await supabase
            .from('workforce')
            .select('id, name, employee_id, position, department')
            .in('id', employeeIds);
          workforceEmployees = workforceData || [];
        } catch (workforceError) {
          console.warn('Could not fetch workforce data:', workforceError);
        }
      }

      // Merge the data
      const enrichedEntries = entriesData?.map(entry => ({
        ...entry,
        user_profile: userProfiles.find(p => p.user_id === entry.user_id),
        employee: workforceEmployees.find(w => w.id === entry.employee_id)
      })) || [];

      console.log(`✅ Fetched ${enrichedEntries.length} payroll entries for period ${payrollPeriodId}`);
      return enrichedEntries;
    } catch (error) {
      console.error('❌ Error fetching payroll entries:', error);
      return [];
    }
  }

  static async updatePayrollEntry(id: string, updates: Partial<PayrollEntry>): Promise<PayrollEntry> {
    try {
      const { data, error } = await supabase
        .from('payroll_entries')
        .update({ ...updates, updated_at: new Date().toISOString() })
        .eq('id', id)
        .select()
        .single();

      if (error) throw error;
      return data;
    } catch (error) {
      console.error('Error updating payroll entry:', error);
      throw error;
    }
  }

  // Financial Integration
  static async createPayrollExpenseEntries(payrollPeriodId: string): Promise<void> {
    try {
      const { error } = await supabase.rpc('create_payroll_expense_entries', {
        p_payroll_period_id: payrollPeriodId
      });

      if (error) {
        // If function doesn't exist, just log warning and continue
        if (error.message.includes('function') && error.message.includes('does not exist')) {
          console.warn('Payroll expense integration function not available:', error.message);
          return; // Don't fail the operation
        }
        throw error;
      }
    } catch (error) {
      console.error('Error creating payroll expense entries:', error);
      // Don't throw - this is a nice-to-have feature
      console.warn('Continuing without payroll expense integration');
    }
  }

  // Enhanced Payroll Generation with Performance Tracking
  static async generateEnhancedPayrollForPeriod(
    periodStart: string,
    periodEnd: string,
    payrollPeriodId: string
  ): Promise<{
    success: boolean;
    message: string;
    payrollEntries: PayrollCalculationResult[];
    performanceInsights: {
      underPerformers: number;
      overAchievers: number;
      averageEfficiency: number;
    };
  }> {
    try {
      console.log('🔄 Generating enhanced payroll with performance tracking...');

      // Calculate enhanced payroll with performance metrics
      const payrollResults = await EnhancedPayrollCalculation.calculatePayrollForPeriod(
        periodStart,
        periodEnd
      );

      // Get performance insights
      const insights = await EnhancedPayrollCalculation.getPerformanceInsights(
        periodStart,
        periodEnd
      );

      // Save the calculated payroll results to the database
      console.log('💾 Saving enhanced payroll results to database...');
      let savedEntries = 0;
      let totalGrossPay = 0;
      let totalNetPay = 0;

      for (const result of payrollResults) {
        try {
          const { data: payrollEntry, error: entryError } = await supabase
            .from('payroll_entries')
            .insert([{
              payroll_period_id: payrollPeriodId,
              employee_id: result.employee_id,
              user_id: result.user_id, // Include user_id for system users
              days_worked: result.days_worked,
              regular_hours: result.regular_hours,
              overtime_hours: result.overtime_hours,
              daily_rate: result.daily_rate,
              hourly_rate: result.daily_rate / 8, // Estimate hourly from daily
              overtime_rate: (result.daily_rate / 8) * 1.5, // 1.5x overtime
              gross_pay: result.gross_pay,
              tax_deductions: result.deductions.tax,
              other_deductions: result.deductions.other,
              net_pay: result.net_pay,
              status: 'calculated',
              performance_rating: result.performance_data.performance_status,
              efficiency_score: result.performance_data.efficiency_score,
              attendance_rate: result.performance_data.attendance_rate
            }])
            .select()
            .single();

          if (entryError) {
            console.error(`❌ Error saving payroll entry for ${result.employee_name}:`, entryError);
            continue;
          }

          savedEntries++;
          totalGrossPay += result.gross_pay;
          totalNetPay += result.net_pay;

          console.log(`✅ Saved enhanced payroll entry for ${result.employee_name}: $${result.gross_pay.toFixed(2)}`);
        } catch (error) {
          console.error(`❌ Error processing payroll for ${result.employee_name}:`, error);
        }
      }

      // Update payroll period totals
      if (savedEntries > 0) {
        const totalDeductions = totalGrossPay - totalNetPay;

        await supabase
          .from('payroll_periods')
          .update({
            total_gross_pay: totalGrossPay,
            total_net_pay: totalNetPay,
            total_deductions: totalDeductions,
            status: 'calculated',
            updated_at: new Date().toISOString()
          })
          .eq('id', payrollPeriodId);

        console.log(`✅ Updated payroll period totals: $${totalGrossPay.toFixed(2)} gross, $${totalNetPay.toFixed(2)} net`);
      }

      // Create payroll entries in database
      const payrollEntries = [];
      for (const result of payrollResults) {
        const { data: entry, error } = await supabase
          .from('payroll_entries')
          .insert([{
            payroll_period_id: payrollPeriodId,
            employee_id: result.employee_id,
            user_id: null, // Will be set if it's a system user
            days_worked: result.days_worked,
            regular_hours: result.regular_hours,
            overtime_hours: result.overtime_hours,
            daily_rate: result.daily_rate,
            hourly_rate: result.daily_rate / 8, // Approximate hourly rate
            overtime_rate: (result.daily_rate / 8) * 1.5, // 1.5x overtime rate
            gross_pay: result.gross_pay,
            tax_deductions: result.deductions.tax,
            other_deductions: result.deductions.insurance + result.deductions.other,
            net_pay: result.net_pay,
            bonus: 0,
            commission: 0,
            status: 'calculated',
            performance_score: result.performance_data.efficiency_score,
            attendance_rate: result.performance_data.attendance_rate,
            performance_notes: `Performance: ${result.performance_data.performance_status.replace('_', ' ')} | Efficiency: ${result.performance_data.efficiency_score.toFixed(1)}%`
          }])
          .select()
          .single();

        if (error) {
          console.error('Error creating payroll entry:', error);
          continue;
        }

        payrollEntries.push(entry);
      }

      // Update payroll period totals
      const totalGross = payrollResults.reduce((sum, result) => sum + result.gross_pay, 0);
      const totalNet = payrollResults.reduce((sum, result) => sum + result.net_pay, 0);
      const totalDeductions = payrollResults.reduce((sum, result) => sum + result.deductions.total, 0);

      await supabase
        .from('payroll_periods')
        .update({
          total_gross_pay: totalGross,
          total_net_pay: totalNet,
          total_deductions: totalDeductions,
          status: 'calculated',
          updated_at: new Date().toISOString()
        })
        .eq('id', payrollPeriodId);

      // If no enhanced entries were saved, try the regular payroll generation for system users
      if (savedEntries === 0) {
        console.log('🔄 No enhanced payroll entries generated, trying regular payroll generation...');

        try {
          const regularResult = await this.generatePayrollEntriesForPeriod(
            payrollPeriodId,
            periodStart,
            periodEnd
          );

          if (regularResult.generated > 0) {
            console.log(`✅ Regular payroll generation completed: ${regularResult.generated} entries saved`);

            return {
              success: true,
              message: `Payroll generated for ${regularResult.generated} employees using regular calculation. Total: $${regularResult.totalGrossPay.toFixed(2)} gross, $${regularResult.totalNetPay.toFixed(2)} net`,
              payrollEntries: payrollResults,
              performanceInsights: {
                underPerformers: insights.underPerformers.length,
                overAchievers: insights.overAchievers.length,
                averageEfficiency: insights.averageEfficiency
              }
            };
          }
        } catch (regularError) {
          console.error('❌ Regular payroll generation also failed:', regularError);
        }
      }

      console.log(`✅ Enhanced payroll generation completed: ${savedEntries} entries saved`);

      return {
        success: savedEntries > 0,
        message: savedEntries > 0
          ? `Enhanced payroll generated for ${savedEntries} employees with performance tracking. Total: $${totalGrossPay.toFixed(2)} gross, $${totalNetPay.toFixed(2)} net`
          : 'No payroll entries were generated. Check if employees have time entries and active contracts for this period.',
        payrollEntries: payrollResults,
        performanceInsights: {
          underPerformers: insights.underPerformers.length,
          overAchievers: insights.overAchievers.length,
          averageEfficiency: insights.averageEfficiency
        }
      };

    } catch (error) {
      console.error('❌ Error generating enhanced payroll:', error);
      return {
        success: false,
        message: `Failed to generate enhanced payroll: ${error instanceof Error ? error.message : 'Unknown error'}`,
        payrollEntries: [],
        performanceInsights: {
          underPerformers: 0,
          overAchievers: 0,
          averageEfficiency: 0
        }
      };
    }
  }

  // Get monthly payroll trend data (using available columns)
  static async getMonthlyPayrollTrend(startDate: string, endDate: string): Promise<any[]> {
    try {
      // First check what columns exist in payroll_entries table
      const { data, error } = await supabase
        .from('payroll_entries')
        .select('*')
        .limit(1);

      if (error) {
        console.warn('Payroll entries table not available:', error.message);
        return [];
      }

      // If table exists but is empty, return empty array
      if (!data || data.length === 0) {
        console.log('No payroll entries found');
        return [];
      }

      // Get payroll data with period information for date filtering
      const { data: payrollData, error: payrollError } = await supabase
        .from('payroll_entries')
        .select(`
          created_at,
          gross_pay,
          net_pay,
          tax_deductions,
          other_deductions,
          payroll_periods!inner(period_start, period_end)
        `)
        .gte('payroll_periods.period_start', startDate)
        .lte('payroll_periods.period_end', endDate)
        .order('created_at');

      if (payrollError) {
        console.warn('Error fetching payroll data:', payrollError.message);
        return [];
      }

      // Group by month and sum values
      const monthlyData = (payrollData || []).reduce((acc: any, entry: any) => {
        const month = new Date(entry.created_at).toLocaleDateString('en-US', { month: 'short' });
        if (!acc[month]) {
          acc[month] = { month, grossPay: 0, netPay: 0, deductions: 0 };
        }
        acc[month].grossPay += entry.gross_pay || 0;
        acc[month].netPay += entry.net_pay || 0;
        acc[month].deductions += (entry.tax_deductions || 0) + (entry.other_deductions || 0);
        return acc;
      }, {});

      return Object.values(monthlyData);
    } catch (error) {
      console.error('Error getting monthly payroll trend:', error);
      return [];
    }
  }

  // Get department payroll distribution (simplified without foreign key)
  static async getDepartmentPayrollDistribution(startDate: string, endDate: string): Promise<any[]> {
    try {
      // Check if payroll_entries table exists and has data
      const { data, error } = await supabase
        .from('payroll_entries')
        .select('*')
        .limit(1);

      if (error) {
        console.warn('Payroll entries table not available:', error.message);
        return [];
      }

      // If no data, return empty array
      if (!data || data.length === 0) {
        console.log('No payroll entries found for department distribution');
        return [];
      }

      // Get payroll data with period information
      const { data: payrollData, error: payrollError } = await supabase
        .from('payroll_entries')
        .select(`
          user_id,
          gross_pay,
          payroll_periods!inner(period_start, period_end)
        `)
        .gte('payroll_periods.period_start', startDate)
        .lte('payroll_periods.period_end', endDate);

      if (payrollError) {
        console.error('Error fetching payroll data:', payrollError);
        return [];
      }

      if (!payrollData || payrollData.length === 0) {
        console.log('No payroll data found for the specified period');
        return [];
      }

      // Get user profiles separately
      const userIds = [...new Set(payrollData.map(entry => entry.user_id))];
      const { data: userProfiles, error: profilesError } = await supabase
        .from('user_profiles')
        .select('user_id, department')
        .in('user_id', userIds);

      if (profilesError) {
        console.warn('Could not load user profiles, using default department:', profilesError);
      }

      // Create a map of user_id to department
      const userDepartmentMap = new Map<string, string>();
      userProfiles?.forEach(profile => {
        userDepartmentMap.set(profile.user_id, profile.department || 'Unassigned');
      });

      // Group by department and calculate totals
      const departmentTotals = new Map<string, number>();
      const colors = ['#3b82f6', '#10b981', '#f59e0b', '#ef4444', '#8b5cf6', '#f97316'];

      payrollData.forEach((entry: any) => {
        const department = userDepartmentMap.get(entry.user_id) || 'Unassigned';
        const current = departmentTotals.get(department) || 0;
        departmentTotals.set(department, current + (entry.gross_pay || 0));
      });

      // Convert to chart format
      const departmentData = Array.from(departmentTotals.entries()).map(([name, value], index) => ({
        name,
        value,
        color: colors[index % colors.length]
      }));

      console.log('📊 Department payroll distribution:', departmentData);
      return departmentData.length > 0 ? departmentData : [];
    } catch (error) {
      console.error('Error getting department payroll distribution:', error);
      return [];
    }
  }

  // Helper method to get consistent colors for departments
  private static getDepartmentColor(department: string): string {
    const colors = {
      'admin': '#3b82f6',
      'management': '#10b981',
      'qs': '#f59e0b',
      'accountant': '#ef4444',
      'client': '#8b5cf6'
    };
    return colors[department.toLowerCase()] || '#6b7280';
  }

  // Analytics and Reporting (Simplified - using time entries and mock data)
  static async getPayrollAnalytics(startDate?: string, endDate?: string): Promise<{
    totalGrossPay: number;
    totalNetPay: number;
    totalDeductions: number;
    totalRegularHours: number;
    totalOvertimeHours: number;
    averageHourlyRate: number;
    employeeCount: number;
    periodCount: number;
  }> {
    try {
      // Get time entries to calculate real hours and pay
      let query = supabase
        .from('time_entries')
        .select('user_id, total_hours, overtime_hours, total_pay, status');

      if (startDate) {
        query = query.gte('clock_in_time', startDate);
      }
      if (endDate) {
        query = query.lte('clock_in_time', endDate);
      }

      const { data: timeEntries, error } = await query.eq('status', 'completed');

      if (error) {
        console.error('❌ Error fetching time entries:', error);
        throw error;
      }

      const entries = timeEntries || [];
      const totalGrossPay = entries.reduce((sum, entry) => sum + (entry.total_pay || 0), 0);
      const totalRegularHours = entries.reduce((sum, entry) => sum + Math.max(0, (entry.total_hours || 0) - (entry.overtime_hours || 0)), 0);
      const totalOvertimeHours = entries.reduce((sum, entry) => sum + (entry.overtime_hours || 0), 0);
      const totalHours = totalRegularHours + totalOvertimeHours;
      const averageHourlyRate = totalHours > 0 ? totalGrossPay / totalHours : 25.00;

      // Calculate deductions (20% tax, 5% other)
      const totalDeductions = totalGrossPay * 0.25;
      const totalNetPay = totalGrossPay - totalDeductions;

      // Get unique employee count
      const uniqueEmployees = new Set(entries.map(e => e.user_id)).size;

      return {
        totalGrossPay,
        totalNetPay,
        totalDeductions,
        totalRegularHours,
        totalOvertimeHours,
        averageHourlyRate,
        employeeCount: uniqueEmployees || 1,
        periodCount: 2
      };
    } catch (error) {
      console.error('❌ Error fetching payroll analytics:', error);
      // Return empty analytics instead of mock data
      return {
        totalGrossPay: 0,
        totalNetPay: 0,
        totalDeductions: 0,
        totalRegularHours: 0,
        totalOvertimeHours: 0,
        averageHourlyRate: 0,
        employeeCount: 0,
        periodCount: 0
      };
    }
  }

  // Time Entry Integration
  static async getEmployeeTimeEntriesForPeriod(
    userId: string,
    startDate: string,
    endDate: string
  ): Promise<TimeEntryWithPay[]> {
    try {
      const { data, error } = await supabase
        .from('time_entries')
        .select(`
          *,
          site:sites(id, name, site_code),
          project:projects(id, name)
        `)
        .eq('user_id', userId)
        .gte('clock_in_time', startDate)
        .lte('clock_in_time', endDate)
        .eq('status', 'completed')
        .order('clock_in_time', { ascending: false });

      if (error) throw error;
      return data || [];
    } catch (error) {
      console.error('Error fetching employee time entries:', error);
      throw error;
    }
  }

  // Automated Payroll Processing (Simplified)
  static async processAutomaticPayroll(): Promise<{
    success: boolean;
    message: string;
    payrollPeriodId?: string;
  }> {
    try {
      // Calculate the current pay period (bi-weekly)
      const today = new Date();
      const startOfYear = new Date(today.getFullYear(), 0, 1);
      const daysSinceStart = Math.floor((today.getTime() - startOfYear.getTime()) / (1000 * 60 * 60 * 24));
      const weekNumber = Math.floor(daysSinceStart / 7);
      const payPeriodNumber = Math.floor(weekNumber / 2);

      const periodStart = new Date(startOfYear);
      periodStart.setDate(periodStart.getDate() + (payPeriodNumber * 14));

      const periodEnd = new Date(periodStart);
      periodEnd.setDate(periodEnd.getDate() + 13);

      const payDate = new Date(periodEnd);
      payDate.setDate(payDate.getDate() + 3); // Pay 3 days after period end

      // Check for existing payroll periods
      const existingPeriods = await this.getPayrollPeriods();
      const periodStartStr = periodStart.toISOString().split('T')[0];
      const periodEndStr = periodEnd.toISOString().split('T')[0];

      const existingPayroll = existingPeriods.find(p =>
        p.period_start === periodStartStr && p.period_end === periodEndStr
      );

      if (existingPayroll) {
        return {
          success: false,
          message: 'Payroll already exists for this period'
        };
      }

      // Create the payroll period
      const payrollPeriod = await this.createPayrollPeriod({
        period_start: periodStartStr,
        period_end: periodEndStr,
        pay_date: payDate.toISOString().split('T')[0]
      });

      // Generate payroll entries using the database function
      console.log('🔄 Generating payroll entries from time tracking data...');

      const { data: generatedPayrollId, error: generateError } = await supabase
        .rpc('generate_payroll_for_period', {
          p_period_start: periodStartStr,
          p_period_end: periodEndStr,
          p_pay_date: payDate.toISOString().split('T')[0]
        });

      if (generateError) {
        console.error('❌ Error generating payroll entries:', generateError);
        // Still return success for period creation, but note the issue
        return {
          success: true,
          message: `Payroll period created for ${periodStart.toLocaleDateString()} - ${periodEnd.toLocaleDateString()}, but payroll entries generation failed. Please generate manually.`,
          payrollPeriodId: payrollPeriod.id
        };
      }

      console.log('✅ Payroll entries generated successfully');

      return {
        success: true,
        message: `Payroll period created and entries generated successfully for ${periodStart.toLocaleDateString()} - ${periodEnd.toLocaleDateString()}`,
        payrollPeriodId: generatedPayrollId || payrollPeriod.id
      };
    } catch (error) {
      console.error('Error processing automatic payroll:', error);
      return {
        success: false,
        message: 'Failed to process automatic payroll'
      };
    }
  }

  // Bulk Time Entry Processing
  static async processAllPendingTimeEntries(): Promise<{
    processed: number;
    failed: number;
    totalAmount: number;
  }> {
    try {
      // Get all completed time entries without pay calculation
      const { data: pendingEntries, error } = await supabase
        .from('time_entries')
        .select('id, user_id, employee_id, total_hours, overtime_hours')
        .eq('status', 'completed')
        .is('total_pay', null);

      if (error) throw error;

      let processed = 0;
      let failed = 0;
      let totalAmount = 0;

      for (const entry of pendingEntries || []) {
        try {
          const processedEntry = await this.processTimeEntryWithPay(entry.id);
          processed++;
          totalAmount += processedEntry.total_pay || 0;
        } catch (error) {
          console.error(`Failed to process time entry ${entry.id}:`, error);
          failed++;
        }
      }

      return { processed, failed, totalAmount };
    } catch (error) {
      console.error('Error processing pending time entries:', error);
      throw error;
    }
  }

  // Get real payroll entries for an employee
  static async getEmployeePayrollHistory(
    employeeId?: string,
    userId?: string,
    limit: number = 10
  ): Promise<any[]> {
    try {
      let query = supabase
        .from('payroll_entries')
        .select(`
          id,
          days_worked,
          regular_hours,
          overtime_hours,
          daily_rate,
          hourly_rate,
          overtime_rate,
          gross_pay,
          tax_deductions,
          other_deductions,
          net_pay,
          bonus,
          commission,
          status,
          created_at,
          payroll_period:payroll_periods(period_start, period_end)
        `)
        .order('created_at', { ascending: false })
        .limit(limit);

      // Filter by employee_id or user_id
      if (employeeId) {
        query = query.eq('employee_id', employeeId);
      } else if (userId) {
        query = query.eq('user_id', userId);
      } else {
        return []; // No identifier provided
      }

      const { data, error } = await query;

      if (error) throw error;

      // Transform data to match the expected format
      return (data || []).map(entry => ({
        id: entry.id,
        date: entry.payroll_period?.period_start || entry.created_at?.split('T')[0] || new Date().toISOString().split('T')[0],
        days_worked: entry.days_worked || 0,
        hours: (entry.regular_hours || 0) + (entry.overtime_hours || 0),
        regular_hours: entry.regular_hours || 0,
        overtime_hours: entry.overtime_hours || 0,
        daily_rate: entry.daily_rate || 0,
        hourly_rate: entry.hourly_rate || 0,
        gross_pay: entry.gross_pay || 0,
        tax_deductions: entry.tax_deductions || 0,
        other_deductions: entry.other_deductions || 0,
        net_pay: entry.net_pay || 0,
        bonus: entry.bonus || 0,
        commission: entry.commission || 0,
        status: entry.status || 'draft',
        period_start: entry.payroll_period?.period_start,
        period_end: entry.payroll_period?.period_end
      }));
    } catch (error) {
      console.error('Error fetching employee payroll history:', error);
      return [];
    }
  }

  // Employee Pay Summary (now using real data)
  static async getEmployeePaySummary(
    userId?: string,
    employeeId?: string,
    startDate?: string,
    endDate?: string
  ): Promise<{
    totalGrossPay: number;
    totalNetPay: number;
    totalHours: number;
    totalOvertimeHours: number;
    averageHourlyRate: number;
    payPeriods: number;
  }> {
    try {
      let query = supabase
        .from('payroll_entries')
        .select(`
          gross_pay,
          net_pay,
          regular_hours,
          overtime_hours,
          daily_rate,
          hourly_rate,
          overtime_rate,
          payroll_period:payroll_periods(period_start, period_end)
        `);

      // Filter by employee_id or user_id
      if (employeeId) {
        query = query.eq('employee_id', employeeId);
      } else if (userId) {
        query = query.eq('user_id', userId);
      } else {
        // No identifier provided, return empty summary
        return {
          totalGrossPay: 0,
          totalNetPay: 0,
          totalHours: 0,
          totalOvertimeHours: 0,
          averageHourlyRate: 0,
          payPeriods: 0
        };
      }

      if (startDate || endDate) {
        query = query.filter('payroll_period.period_start', 'gte', startDate || '1900-01-01');
        query = query.filter('payroll_period.period_end', 'lte', endDate || '2100-12-31');
      }

      const { data, error } = await query;

      if (error) throw error;

      const entries = data || [];
      const totalGrossPay = entries.reduce((sum, entry) => sum + (entry.gross_pay || 0), 0);
      const totalNetPay = entries.reduce((sum, entry) => sum + (entry.net_pay || 0), 0);
      const totalHours = entries.reduce((sum, entry) => sum + (entry.regular_hours || 0), 0);
      const totalOvertimeHours = entries.reduce((sum, entry) => sum + (entry.overtime_hours || 0), 0);
      const allHours = totalHours + totalOvertimeHours;
      const averageHourlyRate = allHours > 0 ? totalGrossPay / allHours : 0;

      return {
        totalGrossPay,
        totalNetPay,
        totalHours,
        totalOvertimeHours,
        averageHourlyRate,
        payPeriods: entries.length
      };
    } catch (error) {
      console.error('Error fetching employee pay summary:', error);
      return {
        totalGrossPay: 0,
        totalNetPay: 0,
        totalHours: 0,
        totalOvertimeHours: 0,
        averageHourlyRate: 0,
        payPeriods: 0
      };
    }
  }

  // Generate Payroll Entries from Time Tracking Data
  static async generatePayrollEntriesForPeriod(
    payrollPeriodId: string,
    periodStart: string,
    periodEnd: string
  ): Promise<{ generated: number; totalGrossPay: number; totalNetPay: number }> {
    try {
      console.log(`🔄 Generating payroll entries for period ${periodStart} to ${periodEnd}...`);

      // Get all time entries for the period (both system users and workforce employees)
      const { data: timeEntriesData, error: timeError } = await supabase
        .from('time_entries')
        .select('*')
        .gte('clock_in_time', periodStart)
        .lte('clock_in_time', periodEnd)
        .eq('status', 'completed');

      if (timeError) {
        console.error('❌ Error fetching time entries:', timeError);
        throw timeError;
      }

      // Get user profiles and workforce data separately
      const userIds = [...new Set(timeEntriesData?.filter(e => e.user_id).map(e => e.user_id) || [])];
      const employeeIds = [...new Set(timeEntriesData?.filter(e => e.employee_id).map(e => e.employee_id) || [])];

      let userProfiles: any[] = [];
      let workforceEmployees: any[] = [];

      // Get user profiles
      if (userIds.length > 0) {
        try {
          const { data: profilesData } = await supabase
            .from('user_profiles')
            .select('user_id, first_name, last_name, employee_id')
            .in('user_id', userIds);
          userProfiles = profilesData || [];
        } catch (profileError) {
          console.warn('Could not fetch user profiles:', profileError);
        }
      }

      // Get workforce employees
      if (employeeIds.length > 0) {
        try {
          const { data: workforceData } = await supabase
            .from('workforce')
            .select('id, name, employee_id, position, department')
            .in('id', employeeIds);
          workforceEmployees = workforceData || [];
        } catch (workforceError) {
          console.warn('Could not fetch workforce data:', workforceError);
        }
      }

      // Merge the data
      const timeEntries = timeEntriesData?.map(entry => ({
        ...entry,
        user_profile: userProfiles.find(p => p.user_id === entry.user_id),
        employee: workforceEmployees.find(w => w.id === entry.employee_id)
      })) || [];

      // Group time entries by employee (handle both system users and workforce employees)
      const employeeTimeMap = new Map();

      for (const entry of timeEntries || []) {
        // Determine the unique employee key - prioritize employee_id for workforce, fallback to user_id
        let employeeKey = entry.employee_id || entry.user_id;
        if (!employeeKey) continue;

        // Create a composite key to handle cases where someone might have both employee_id and user_id
        const compositeKey = `${entry.employee_id || 'no-emp'}_${entry.user_id || 'no-user'}`;

        if (!employeeTimeMap.has(compositeKey)) {
          employeeTimeMap.set(compositeKey, {
            employee_id: entry.employee_id,
            user_id: entry.user_id,
            employee: entry.employee,
            user_profile: entry.user_profile,
            entries: [],
            totalHours: 0,
            totalOvertimeHours: 0,
            totalPay: 0,
            daysWorked: 0,
            employeeName: entry.employee?.name ||
                         (entry.user_profile ? `${entry.user_profile.first_name} ${entry.user_profile.last_name}` : 'Unknown'),
            isWorkforceEmployee: !!entry.employee_id,
            isSystemUser: !!entry.user_id && !!entry.user_profile
          });
        }

        const employeeData = employeeTimeMap.get(compositeKey);
        employeeData.entries.push(entry);
        employeeData.totalHours += entry.total_hours || 0;
        employeeData.totalOvertimeHours += entry.overtime_hours || 0;
        employeeData.totalPay += entry.total_pay || 0;
        employeeData.daysWorked += entry.days_worked || 1;
      }

      let generated = 0;
      let totalGrossPay = 0;
      let totalNetPay = 0;

      // Generate payroll entry for each employee
      for (const [employeeKey, employeeData] of employeeTimeMap) {
        try {
          // Get employee contract data
          const contract = await this.getEmployeeContract(
            employeeData.employee_id,
            employeeData.user_id
          );

          // Use contract rates if available, otherwise skip this employee (no contract = no pay calculation)
          if (!contract) {
            console.warn(`⚠️ No contract found for employee ${employeeKey}, skipping payroll calculation`);
            continue;
          }

          const dailyRate = contract.daily_rate;
          const hourlyRate = contract.hourly_rate;
          const overtimeRate = contract.overtime_rate;

          // Calculate regular hours (total - overtime)
          const regularHours = Math.max(0, employeeData.totalHours - employeeData.totalOvertimeHours);

          // Calculate gross pay
          const grossPay = employeeData.totalPay ||
            (employeeData.daysWorked * dailyRate) +
            (employeeData.totalOvertimeHours * overtimeRate);

          // Calculate deductions (20% tax, 5% other)
          const taxDeductions = grossPay * 0.20;
          const otherDeductions = grossPay * 0.05;
          const netPay = grossPay - taxDeductions - otherDeductions;

          // Create payroll entry
          const { data: payrollEntry, error: entryError } = await supabase
            .from('payroll_entries')
            .insert([{
              payroll_period_id: payrollPeriodId,
              employee_id: employeeData.employee_id,
              user_id: employeeData.user_id,
              days_worked: employeeData.daysWorked,
              regular_hours: regularHours,
              overtime_hours: employeeData.totalOvertimeHours,
              daily_rate: dailyRate,
              hourly_rate: hourlyRate,
              overtime_rate: overtimeRate,
              gross_pay: grossPay,
              tax_deductions: taxDeductions,
              other_deductions: otherDeductions,
              net_pay: netPay,
              status: 'calculated'
            }])
            .select()
            .single();

          if (entryError) {
            console.error(`❌ Error creating payroll entry for employee ${employeeKey}:`, entryError);
            continue;
          }

          generated++;
          totalGrossPay += grossPay;
          totalNetPay += netPay;

          console.log(`✅ Generated payroll entry for ${employeeData.employeeName} (${employeeData.isWorkforceEmployee ? 'Workforce' : 'System User'}): $${grossPay.toFixed(2)}`);
        } catch (error) {
          console.error(`❌ Error processing employee ${employeeData.employeeName}:`, error);
        }
      }

      // Update payroll period totals
      if (generated > 0) {
        const totalDeductions = totalGrossPay - totalNetPay;

        await supabase
          .from('payroll_periods')
          .update({
            total_gross_pay: totalGrossPay,
            total_net_pay: totalNetPay,
            total_deductions: totalDeductions,
            status: 'calculated',
            updated_at: new Date().toISOString()
          })
          .eq('id', payrollPeriodId);

        console.log(`✅ Updated payroll period totals: $${totalGrossPay.toFixed(2)} gross, $${totalNetPay.toFixed(2)} net`);
      }

      console.log(`✅ Generated ${generated} payroll entries for period`);
      return { generated, totalGrossPay, totalNetPay };
    } catch (error) {
      console.error('❌ Error generating payroll entries:', error);
      throw error;
    }
  }
}
