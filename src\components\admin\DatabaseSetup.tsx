import React, { useState } from 'react'
import { Card, Card<PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { 
  Database, 
  Copy, 
  CheckCircle,
  AlertTriangle,
  Info,
  ExternalLink
} from 'lucide-react'
import { useToast } from '@/components/ui/use-toast'

export default function DatabaseSetup() {
  const [copiedQuery, setCopiedQuery] = useState<string | null>(null)
  const { toast } = useToast()

  const copyToClipboard = async (text: string, queryName: string) => {
    try {
      await navigator.clipboard.writeText(text)
      setCopiedQuery(queryName)
      setTimeout(() => setCopiedQuery(null), 2000)
      
      toast({
        title: "Copied to Clipboard",
        description: `${queryName} SQL query copied successfully`,
      })
    } catch (error) {
      toast({
        title: "Copy Failed",
        description: "Failed to copy to clipboard",
        variant: "destructive"
      })
    }
  }

  const companyExpenseCategoriesSQL = `-- Create company expense categories table
CREATE TABLE IF NOT EXISTS company_expense_categories (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  name VARCHAR(255) NOT NULL,
  description TEXT,
  color VARCHAR(7) NOT NULL DEFAULT '#3B82F6',
  icon VARCHAR(10) NOT NULL DEFAULT '📦',
  is_project_related BOOLEAN NOT NULL DEFAULT false,
  is_active BOOLEAN NOT NULL DEFAULT true,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_company_expense_categories_active 
ON company_expense_categories(is_active);

CREATE INDEX IF NOT EXISTS idx_company_expense_categories_name 
ON company_expense_categories(name);`

  const insertDefaultCategoriesSQL = `-- Insert default company expense categories
INSERT INTO company_expense_categories (name, description, color, icon, is_project_related) VALUES
('Office Supplies', 'Stationery, equipment, and office materials', '#3B82F6', '📎', false),
('Equipment & Tools', 'Construction tools, machinery, and equipment', '#F59E0B', '🔧', true),
('Materials & Supplies', 'Construction materials and project supplies', '#10B981', '🧱', true),
('Utilities', 'Electricity, water, internet, and other utilities', '#EF4444', '💡', false),
('Transportation', 'Vehicle expenses, fuel, and transportation costs', '#8B5CF6', '🚗', false),
('Professional Services', 'Legal, accounting, consulting, and other professional services', '#06B6D4', '💼', false),
('Marketing & Advertising', 'Marketing campaigns, advertising, and promotional materials', '#EC4899', '📢', false),
('Insurance', 'Business insurance, liability, and coverage costs', '#84CC16', '🛡️', false),
('Maintenance & Repairs', 'Equipment maintenance, repairs, and upkeep', '#F97316', '🔨', true),
('Other Expenses', 'Miscellaneous business expenses', '#6B7280', '📦', false)
ON CONFLICT (name) DO NOTHING;`

  const updateExpensesTableSQL = `-- Add missing columns to expenses table (optional - for enhanced features)
ALTER TABLE expenses
ADD COLUMN IF NOT EXISTS project_id UUID REFERENCES projects(id),
ADD COLUMN IF NOT EXISTS receipt_url TEXT,
ADD COLUMN IF NOT EXISTS metadata JSONB;

-- Create index for project_id if column was added
CREATE INDEX IF NOT EXISTS idx_expenses_project_id ON expenses(project_id);`

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center">
          <Database className="w-5 h-5 mr-2" />
          Database Setup - Company Expenses
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-6">
        {/* Status Alert */}
        <div className="flex items-start space-x-3 p-4 bg-yellow-50 rounded-lg border border-yellow-200">
          <AlertTriangle className="w-5 h-5 text-yellow-600 mt-0.5" />
          <div className="flex-1">
            <h4 className="font-medium text-yellow-900">Database Table Missing</h4>
            <p className="text-sm text-yellow-800 mt-1">
              The <code className="bg-yellow-100 px-1 rounded">company_expense_categories</code> table is missing. 
              Run the SQL queries below in your Supabase SQL Editor to set up company expense management.
            </p>
          </div>
        </div>

        {/* Instructions */}
        <div className="space-y-4">
          <div className="flex items-start space-x-3 p-4 bg-blue-50 rounded-lg">
            <Info className="w-5 h-5 text-blue-600 mt-0.5" />
            <div className="flex-1">
              <h4 className="font-medium text-blue-900">Setup Instructions</h4>
              <ol className="text-sm text-blue-800 mt-2 space-y-1 list-decimal list-inside">
                <li>Go to your Supabase Dashboard → SQL Editor</li>
                <li>Copy and run the "Create Table" query first</li>
                <li>Copy and run the "Insert Default Categories" query second</li>
                <li>Copy and run the "Update Expenses Table" query third (optional)</li>
                <li>Refresh this page to start using company expenses</li>
              </ol>
            </div>
          </div>
        </div>

        {/* SQL Queries */}
        <div className="space-y-6">
          {/* Create Table Query */}
          <div className="space-y-3">
            <div className="flex items-center justify-between">
              <h4 className="font-medium">1. Create Table Query</h4>
              <Button
                variant="outline"
                size="sm"
                onClick={() => copyToClipboard(companyExpenseCategoriesSQL, 'Create Table')}
              >
                {copiedQuery === 'Create Table' ? (
                  <CheckCircle className="w-4 h-4 mr-2 text-green-600" />
                ) : (
                  <Copy className="w-4 h-4 mr-2" />
                )}
                Copy SQL
              </Button>
            </div>
            <div className="bg-gray-900 text-gray-100 p-4 rounded-lg overflow-x-auto">
              <pre className="text-sm whitespace-pre-wrap">{companyExpenseCategoriesSQL}</pre>
            </div>
          </div>

          {/* Insert Default Categories Query */}
          <div className="space-y-3">
            <div className="flex items-center justify-between">
              <h4 className="font-medium">2. Insert Default Categories Query</h4>
              <Button
                variant="outline"
                size="sm"
                onClick={() => copyToClipboard(insertDefaultCategoriesSQL, 'Insert Categories')}
              >
                {copiedQuery === 'Insert Categories' ? (
                  <CheckCircle className="w-4 h-4 mr-2 text-green-600" />
                ) : (
                  <Copy className="w-4 h-4 mr-2" />
                )}
                Copy SQL
              </Button>
            </div>
            <div className="bg-gray-900 text-gray-100 p-4 rounded-lg overflow-x-auto">
              <pre className="text-sm whitespace-pre-wrap">{insertDefaultCategoriesSQL}</pre>
            </div>
          </div>

          {/* Update Expenses Table Query */}
          <div className="space-y-3">
            <div className="flex items-center justify-between">
              <h4 className="font-medium">3. Update Expenses Table Query (Optional)</h4>
              <Button
                variant="outline"
                size="sm"
                onClick={() => copyToClipboard(updateExpensesTableSQL, 'Update Expenses Table')}
              >
                {copiedQuery === 'Update Expenses Table' ? (
                  <CheckCircle className="w-4 h-4 mr-2 text-green-600" />
                ) : (
                  <Copy className="w-4 h-4 mr-2" />
                )}
                Copy SQL
              </Button>
            </div>
            <div className="bg-gray-900 text-gray-100 p-4 rounded-lg overflow-x-auto">
              <pre className="text-sm whitespace-pre-wrap">{updateExpensesTableSQL}</pre>
            </div>
            <div className="p-3 bg-blue-50 rounded border border-blue-200">
              <p className="text-sm text-blue-800">
                <strong>Note:</strong> This query adds optional columns for enhanced features like project linking and receipt attachments.
                The system works without these columns, but they enable additional functionality.
              </p>
            </div>
          </div>
        </div>

        {/* Quick Access */}
        <div className="p-4 bg-gray-50 rounded-lg">
          <h4 className="font-medium mb-3">Quick Access</h4>
          <div className="flex space-x-3">
            <Button
              variant="outline"
              size="sm"
              onClick={() => window.open('https://supabase.com/dashboard/project/ygdaucsngasdutbvmevs/sql', '_blank')}
            >
              <ExternalLink className="w-4 h-4 mr-2" />
              Open SQL Editor
            </Button>
            <Button
              variant="outline"
              size="sm"
              onClick={() => window.location.reload()}
            >
              <Database className="w-4 h-4 mr-2" />
              Refresh Page
            </Button>
          </div>
        </div>

        {/* Default Categories Preview */}
        <div className="space-y-3">
          <h4 className="font-medium">Default Categories Preview</h4>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
            {[
              { name: 'Office Supplies', icon: '📎', color: '#3B82F6', project: false },
              { name: 'Equipment & Tools', icon: '🔧', color: '#F59E0B', project: true },
              { name: 'Materials & Supplies', icon: '🧱', color: '#10B981', project: true },
              { name: 'Utilities', icon: '💡', color: '#EF4444', project: false },
              { name: 'Transportation', icon: '🚗', color: '#8B5CF6', project: false },
              { name: 'Professional Services', icon: '💼', color: '#06B6D4', project: false },
              { name: 'Marketing & Advertising', icon: '📢', color: '#EC4899', project: false },
              { name: 'Insurance', icon: '🛡️', color: '#84CC16', project: false },
              { name: 'Maintenance & Repairs', icon: '🔨', color: '#F97316', project: true },
              { name: 'Other Expenses', icon: '📦', color: '#6B7280', project: false }
            ].map((category, index) => (
              <div key={index} className="flex items-center space-x-3 p-3 bg-white rounded border">
                <div
                  className="w-8 h-8 rounded-full flex items-center justify-center text-white"
                  style={{ backgroundColor: category.color }}
                >
                  {category.icon}
                </div>
                <div className="flex-1">
                  <div className="font-medium text-sm">{category.name}</div>
                  <div className="flex space-x-2">
                    <Badge variant={category.project ? "default" : "secondary"} className="text-xs">
                      {category.project ? 'Project-related' : 'General'}
                    </Badge>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* Success Message */}
        <div className="p-4 bg-green-50 rounded-lg border border-green-200">
          <div className="flex items-start space-x-3">
            <CheckCircle className="w-5 h-5 text-green-600 mt-0.5" />
            <div>
              <h4 className="font-medium text-green-900">After Setup</h4>
              <p className="text-sm text-green-800 mt-1">
                Once you've run the required SQL queries, you'll be able to:
              </p>
              <ul className="text-sm text-green-800 mt-2 space-y-1 list-disc list-inside ml-4">
                <li>Create and manage company expense categories</li>
                <li>Add company expenses with proper categorization</li>
                <li>Track project-related vs general business expenses</li>
                <li>Customize categories with colors and icons</li>
                <li>Link expenses to specific projects (with optional query 3)</li>
                <li>Attach receipts and metadata (with optional query 3)</li>
              </ul>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  )
}
