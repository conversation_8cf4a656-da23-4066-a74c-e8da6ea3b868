import React, { useState, useEffect } from 'react'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { CompanyExpenseService } from '@/lib/companyExpenseService'

export default function ExpenseDebug() {
  const [categories, setCategories] = useState<any[]>([])
  const [loading, setLoading] = useState(false)
  const [testResult, setTestResult] = useState<string>('')

  const loadCategories = async () => {
    setLoading(true)
    try {
      const cats = await CompanyExpenseService.getCompanyExpenseCategories()
      setCategories(cats)
      console.log('Loaded categories:', cats)
    } catch (error) {
      console.error('Error loading categories:', error)
      setTestResult(`Error: ${error}`)
    } finally {
      setLoading(false)
    }
  }

  const testExpenseCreation = async () => {
    if (categories.length === 0) {
      setTestResult('No categories available for testing')
      return
    }

    try {
      const testExpense = {
        category_id: categories[0].id,
        description: 'Test expense',
        amount: 100,
        expense_date: new Date().toISOString().split('T')[0],
        payment_method: 'Cash',
        vendor_name: 'Test Vendor',
        status: 'pending' as const,
        is_recurring: false,
        recurring_frequency: undefined,
        project_id: undefined,
        receipt_url: undefined,
        metadata: undefined
      }

      console.log('Testing expense creation with:', testExpense)
      const result = await CompanyExpenseService.createCompanyExpense(testExpense)
      setTestResult(`Success! Created expense with ID: ${result.id}`)
      console.log('Created expense:', result)
    } catch (error) {
      console.error('Test expense creation failed:', error)
      setTestResult(`Failed: ${error}`)
    }
  }

  useEffect(() => {
    loadCategories()
  }, [])

  return (
    <Card className="max-w-2xl">
      <CardHeader>
        <CardTitle>Company Expense Debug</CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        <div className="flex space-x-2">
          <Button onClick={loadCategories} disabled={loading}>
            {loading ? 'Loading...' : 'Reload Categories'}
          </Button>
          <Button onClick={testExpenseCreation} disabled={categories.length === 0}>
            Test Expense Creation
          </Button>
        </div>

        {testResult && (
          <div className={`p-3 rounded ${testResult.includes('Success') ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'}`}>
            {testResult}
          </div>
        )}

        <div>
          <h4 className="font-medium mb-2">Categories ({categories.length})</h4>
          <div className="space-y-2 max-h-64 overflow-y-auto">
            {categories.map((category, index) => (
              <div key={index} className="flex items-center space-x-3 p-2 bg-gray-50 rounded">
                <div
                  className="w-6 h-6 rounded-full flex items-center justify-center text-white text-xs"
                  style={{ backgroundColor: category.color }}
                >
                  {category.icon}
                </div>
                <div className="flex-1">
                  <div className="font-medium text-sm">{category.name}</div>
                  <div className="text-xs text-gray-500 font-mono">{category.id}</div>
                </div>
                <Badge variant={category.is_project_related ? "default" : "secondary"}>
                  {category.is_project_related ? 'Project' : 'General'}
                </Badge>
              </div>
            ))}
          </div>
        </div>

        {categories.length === 0 && !loading && (
          <div className="p-4 bg-yellow-50 rounded border border-yellow-200">
            <p className="text-yellow-800">
              No categories loaded. This could mean:
            </p>
            <ul className="text-sm text-yellow-700 mt-2 list-disc list-inside">
              <li>Database table doesn't exist (expected - using defaults)</li>
              <li>Network error</li>
              <li>Permission issue</li>
            </ul>
          </div>
        )}
      </CardContent>
    </Card>
  )
}
