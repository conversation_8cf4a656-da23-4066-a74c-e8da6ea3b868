import { supabase } from './supabase';
import { NotificationIntegration } from './notificationIntegration';
import { TimeTrackingService } from './timeTrackingService';

// Database types
export interface Project {
  id: string;
  name: string;
  description: string;
  status: string;
  priority: string;
  start_date: string;
  end_date: string;
  budget: number;
  spent: number;
  progress: number;
  client_name: string;
  client_id?: string; // Foreign key to clients table
  project_manager: string;
  location: string;
  date_added: string;
  last_modified: string;
  created_at: string;
  // Creator information
  created_by_user_id?: string;
  created_by_name?: string;
  created_by_avatar?: string;
}

// Project status options
export const PROJECT_STATUSES = [
  'Planning',
  'In Progress',
  'On Hold',
  'Completed',
  'Cancelled'
];

// Priority options
export const PROJECT_PRIORITIES = [
  'Low',
  'Medium',
  'High',
  'Critical'
];

// Database operations
export class ProjectService {
  // Test database connection
  static async testConnection(): Promise<{ success: boolean; message: string; details?: any }> {
    try {
      console.log('Testing projects table connection...');
      
      const { data, error } = await supabase
        .from('projects')
        .select('count', { count: 'exact', head: true });
      
      if (error) {
        console.error('Projects connection error:', error);
        return {
          success: false,
          message: `Database error: ${error.message}`,
          details: error
        };
      }
      
      console.log('Projects connection test successful');
      return {
        success: true,
        message: 'Successfully connected to projects table',
        details: { count: data }
      };
    } catch (error) {
      console.error('Projects connection test failed:', error);
      return {
        success: false,
        message: error instanceof Error ? error.message : 'Unknown connection error',
        details: error
      };
    }
  }

  // Get projects based on user role and access permissions
  static async getProjects(userId?: string): Promise<Project[]> {
    try {
      console.log('Fetching projects from Supabase with role-based access...');

      // Use RLS (Row Level Security) for automatic filtering
      // The database policies will automatically filter based on the authenticated user
      const { data, error } = await supabase
        .from('projects')
        .select('*')
        .order('date_added', { ascending: false });

      if (error) {
        console.error('Error fetching projects:', error);
        throw new Error(`Database error: ${error.message}`);
      }

      console.log('Successfully fetched role-based projects:', data);
      return data || [];
    } catch (error) {
      console.error('Failed to fetch projects:', error);
      throw error;
    }
  }

  // Get all projects (internal method, no role filtering)
  static async getAllProjects(): Promise<Project[]> {
    try {
      const { data, error } = await supabase
        .from('projects')
        .select('*')
        .order('date_added', { ascending: false });

      if (error) {
        console.error('Error fetching all projects:', error);
        throw new Error(`Database error: ${error.message}`);
      }

      console.log('Successfully fetched all projects:', data);
      return data || [];
    } catch (error) {
      console.error('Failed to fetch all projects:', error);
      throw error;
    }
  }

  // Check if user can access a specific project
  static async canUserAccessProject(projectId: string, userId?: string): Promise<boolean> {
    try {
      if (!userId) {
        console.log('No user ID provided for access check');
        return false;
      }

      const { data, error } = await supabase
        .rpc('can_user_access_project', {
          project_id: projectId,
          target_user_id: userId
        });

      if (error) {
        console.error('Error checking project access:', error);
        return false;
      }

      return data || false;
    } catch (error) {
      console.error('Failed to check project access:', error);
      return false;
    }
  }

  // Get user's client relationships
  static async getUserClientRelationships(userId: string): Promise<any[]> {
    try {
      const { data, error } = await supabase
        .from('client_users')
        .select(`
          *,
          client:clients(*)
        `)
        .eq('user_id', userId);

      if (error) {
        console.error('Error fetching user client relationships:', error);
        return [];
      }

      return data || [];
    } catch (error) {
      console.error('Failed to fetch user client relationships:', error);
      return [];
    }
  }

  // Link user to client (admin only)
  static async linkUserToClient(
    userId: string,
    clientId: string,
    role: string = 'client',
    accessLevel: string = 'read',
    isPrimary: boolean = false
  ): Promise<boolean> {
    try {
      const { data, error } = await supabase
        .rpc('link_user_to_client', {
          target_user_id: userId,
          target_client_id: clientId,
          user_role: role,
          access_level: accessLevel,
          is_primary: isPrimary
        });

      if (error) {
        console.error('Error linking user to client:', error);
        throw new Error(`Failed to link user to client: ${error.message}`);
      }

      return data || false;
    } catch (error) {
      console.error('Failed to link user to client:', error);
      throw error;
    }
  }

  // Validation helper for project data
  static validateProjectData(projectData: Partial<Project>): { isValid: boolean; errors: string[] } {
    const errors: string[] = [];

    // Required field validation
    if (!projectData.name || projectData.name.trim().length === 0) {
      errors.push('Project name is required');
    }

    if (!projectData.client_name || projectData.client_name.trim().length === 0) {
      errors.push('Client name is required');
    }

    if (!projectData.budget || projectData.budget <= 0) {
      errors.push('Valid budget amount is required');
    }

    if (!projectData.start_date) {
      errors.push('Start date is required');
    }

    // Date validation
    if (projectData.start_date && projectData.end_date) {
      const startDate = new Date(projectData.start_date);
      const endDate = new Date(projectData.end_date);

      if (endDate <= startDate) {
        errors.push('End date must be after start date');
      }
    }

    // Budget validation
    if (projectData.budget && projectData.spent && projectData.spent > projectData.budget) {
      errors.push('Spent amount cannot exceed budget');
    }

    return {
      isValid: errors.length === 0,
      errors
    };
  }

  // Check for duplicate projects
  static async checkDuplicateProject(name: string, clientName: string, excludeId?: string): Promise<boolean> {
    try {
      let query = supabase
        .from('projects')
        .select('id, name, client_name')
        .eq('name', name.trim())
        .eq('client_name', clientName.trim());

      if (excludeId) {
        query = query.neq('id', excludeId);
      }

      const { data, error } = await query;

      if (error) {
        console.error('Error checking duplicate project:', error);
        return false;
      }

      return (data && data.length > 0);
    } catch (error) {
      console.error('Error in checkDuplicateProject:', error);
      return false;
    }
  }

  // Add new project with validation and duplicate prevention
  static async addProject(project: Omit<Project, 'id' | 'date_added' | 'last_modified' | 'created_at'>, created_by?: string): Promise<Project> {
    try {
      // Validate project data
      const validation = this.validateProjectData(project);
      if (!validation.isValid) {
        throw new Error(`Validation failed: ${validation.errors.join(', ')}`);
      }

      // Check for duplicates
      const isDuplicate = await this.checkDuplicateProject(project.name, project.client_name);
      if (isDuplicate) {
        throw new Error(`A project named "${project.name}" for client "${project.client_name}" already exists`);
      }

      const { data, error } = await supabase
        .from('projects')
        .insert([{
          name: project.name.trim(),
          description: project.description?.trim(),
          status: project.status || 'Planning',
          priority: project.priority || 'Medium',
          start_date: project.start_date,
          end_date: project.end_date,
          budget: project.budget,
          spent: project.spent || 0,
          progress: project.progress || 0,
          client_name: project.client_name.trim(),
          project_manager: project.project_manager?.trim(),
          location: project.location?.trim(),
          created_by_user_id: created_by
        }])
        .select()
        .single();

      if (error) {
        console.error('Error adding project:', error);
        throw error;
      }

      // Send notification about project creation
      if (created_by) {
        try {
          await NotificationIntegration.handleProjectCreated(data, created_by);
        } catch (notificationError) {
          console.error('Error sending project creation notification:', notificationError);
          // Don't fail the project creation if notification fails
        }
      }

      // Automatically create a site for this project for time tracking
      try {
        await this.createProjectSite(data);
        console.log('✅ Automatically created site for project:', data.name);
      } catch (siteError) {
        console.error('Error creating site for project:', siteError);
        // Don't fail the project creation if site creation fails
      }

      return data;
    } catch (error) {
      console.error('Failed to add project:', error);
      throw error;
    }
  }

  // Get a single project by ID
  static async getProjectById(id: string): Promise<Project> {
    try {
      const { data, error } = await supabase
        .from('projects')
        .select('*')
        .eq('id', id)
        .single();

      if (error) {
        console.error('Error fetching project by ID:', error);
        throw error;
      }

      return data;
    } catch (error) {
      console.error('Error in getProjectById:', error);
      throw error;
    }
  }

  // Update project with validation and duplicate prevention
  static async updateProject(id: string, updates: Partial<Omit<Project, 'id' | 'date_added' | 'created_at'>>, updated_by?: string): Promise<Project> {
    try {
      // Get the original project to compare changes
      const originalProject = await this.getProjectById(id);

      // Validate updated data (merge with original for complete validation)
      const mergedData = { ...originalProject, ...updates };
      const validation = this.validateProjectData(mergedData);
      if (!validation.isValid) {
        throw new Error(`Validation failed: ${validation.errors.join(', ')}`);
      }

      // Check for duplicates if name or client is being changed
      if ((updates.name && updates.name !== originalProject.name) ||
          (updates.client_name && updates.client_name !== originalProject.client_name)) {
        const newName = updates.name || originalProject.name;
        const newClientName = updates.client_name || originalProject.client_name;

        const isDuplicate = await this.checkDuplicateProject(newName, newClientName, id);
        if (isDuplicate) {
          throw new Error(`A project named "${newName}" for client "${newClientName}" already exists`);
        }
      }

      const { data, error } = await supabase
        .from('projects')
        .update({
          ...updates,
          last_modified: new Date().toISOString()
        })
        .eq('id', id)
        .select()
        .single();

      if (error) {
        console.error('Error updating project:', error);
        throw error;
      }

      // Send notification about project update
      if (updated_by && originalProject) {
        try {
          const changes: string[] = [];

          // Detect what changed
          if (updates.status && updates.status !== originalProject.status) {
            changes.push(`Status changed from ${originalProject.status} to ${updates.status}`);
          }
          if (updates.priority && updates.priority !== originalProject.priority) {
            changes.push(`Priority changed from ${originalProject.priority} to ${updates.priority}`);
          }
          if (updates.progress !== undefined && updates.progress !== originalProject.progress) {
            changes.push(`Progress updated to ${updates.progress}%`);
          }
          if (updates.budget && updates.budget !== originalProject.budget) {
            changes.push(`Budget updated to $${updates.budget.toLocaleString()}`);
          }
          if (updates.end_date && updates.end_date !== originalProject.end_date) {
            changes.push(`End date changed to ${updates.end_date}`);
          }

          if (changes.length > 0) {
            await NotificationIntegration.handleProjectUpdated(data, updated_by, changes);
          }
        } catch (notificationError) {
          console.error('Error sending project update notification:', notificationError);
          // Don't fail the project update if notification fails
        }
      }

      return data;
    } catch (error) {
      console.error('Failed to update project:', error);
      throw error;
    }
  }

  // Delete project
  static async deleteProject(id: string): Promise<void> {
    try {
      const { error } = await supabase
        .from('projects')
        .delete()
        .eq('id', id);
      
      if (error) {
        console.error('Error deleting project:', error);
        throw error;
      }
    } catch (error) {
      console.error('Failed to delete project:', error);
      throw error;
    }
  }

  // Subscribe to real-time changes
  static subscribeToChanges(callback: (payload: any) => void) {
    return supabase
      .channel('projects')
      .on('postgres_changes', 
        { 
          event: '*', 
          schema: 'public', 
          table: 'projects' 
        }, 
        callback
      )
      .subscribe();
  }

  // Export data to CSV
  static async exportToCSV(): Promise<string> {
    try {
      const projects = await this.getProjects();
      const headers = [
        'Name', 'Description', 'Status', 'Priority', 'Start Date', 'End Date', 
        'Budget', 'Spent', 'Progress', 'Client', 'Project Manager', 'Location', 'Date Added'
      ];
      
      const csvContent = [
        headers.join(','),
        ...projects.map(project => [
          `"${project.name}"`,
          `"${project.description}"`,
          `"${project.status}"`,
          `"${project.priority}"`,
          project.start_date,
          project.end_date,
          project.budget,
          project.spent,
          project.progress,
          `"${project.client_name}"`,
          `"${project.project_manager}"`,
          `"${project.location}"`,
          new Date(project.date_added).toLocaleDateString()
        ].join(','))
      ].join('\n');

      return csvContent;
    } catch (error) {
      console.error('Failed to export projects data:', error);
      throw error;
    }
  }

  // Get summary statistics
  static async getSummaryStats() {
    try {
      const projects = await this.getProjects();
      
      const totalProjects = projects.length;
      const totalBudget = projects.reduce((sum, project) => sum + Number(project.budget), 0);
      const totalSpent = projects.reduce((sum, project) => sum + Number(project.spent), 0);
      const averageProgress = totalProjects > 0 ? projects.reduce((sum, project) => sum + project.progress, 0) / totalProjects : 0;
      
      // Group by status
      const statusStats = projects.reduce((acc, project) => {
        if (!acc[project.status]) {
          acc[project.status] = { count: 0, budget: 0, spent: 0 };
        }
        acc[project.status].count++;
        acc[project.status].budget += Number(project.budget);
        acc[project.status].spent += Number(project.spent);
        return acc;
      }, {} as Record<string, { count: number; budget: number; spent: number }>);

      // Group by priority
      const priorityStats = projects.reduce((acc, project) => {
        if (!acc[project.priority]) {
          acc[project.priority] = { count: 0, budget: 0 };
        }
        acc[project.priority].count++;
        acc[project.priority].budget += Number(project.budget);
        return acc;
      }, {} as Record<string, { count: number; budget: number }>);

      return {
        totalProjects,
        totalBudget,
        totalSpent,
        averageProgress,
        statusStats,
        priorityStats
      };
    } catch (error) {
      console.error('Failed to get projects summary stats:', error);
      throw error;
    }
  }

  // Automatically create a site for time tracking when a project is created
  static async createProjectSite(project: Project): Promise<void> {
    try {
      // Generate a unique site code based on project name
      const siteCode = this.generateSiteCode(project.name);

      // Check if a site already exists for this project
      const { data: existingSite, error: checkError } = await supabase
        .from('sites')
        .select('id')
        .eq('project_id', project.id)
        .single();

      if (checkError && checkError.code !== 'PGRST116') {
        // PGRST116 is "not found" error, which is expected
        throw checkError;
      }

      if (existingSite) {
        console.log('Site already exists for project:', project.name);
        return;
      }

      // Create the site
      await TimeTrackingService.createSite({
        name: project.name,
        site_code: siteCode,
        address: project.location || undefined,
        description: `Automatically created site for project: ${project.name}`,
        project_id: project.id,
        is_active: true
      });

      console.log('✅ Created site for project:', project.name, 'with code:', siteCode);
    } catch (error) {
      console.error('Error creating site for project:', error);
      throw error;
    }
  }

  // Generate a unique site code from project name
  static generateSiteCode(projectName: string): string {
    // Remove special characters and spaces, take first 8 characters, add timestamp
    const cleanName = projectName
      .replace(/[^a-zA-Z0-9]/g, '')
      .toUpperCase()
      .substring(0, 8);

    const timestamp = Date.now().toString().slice(-4);
    return `${cleanName}-${timestamp}`;
  }

  // Bulk create sites for existing projects that don't have them
  static async createSitesForExistingProjects(): Promise<{ created: number; errors: string[] }> {
    const errors: string[] = [];
    let created = 0;

    try {
      // Get all projects
      const projects = await this.getProjects();

      for (const project of projects) {
        try {
          await this.createProjectSite(project);
          created++;
        } catch (error) {
          const errorMsg = `Failed to create site for project ${project.name}: ${error.message}`;
          errors.push(errorMsg);
          console.error(errorMsg);
        }
      }

      return { created, errors };
    } catch (error) {
      errors.push(`Failed to get projects: ${error.message}`);
      return { created, errors };
    }
  }
}
